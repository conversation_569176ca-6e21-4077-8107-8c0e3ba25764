package com.mega.platform.cloud.data.vo.microservice;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

@Data
@Schema(description = "创建服务组请求参数")
public class CreateServicesGroupReqVO {

    @Schema(description = "模板参数值")
    private Map<String, String> jenkinsParams;

    @NotNull
    @Schema(description = "管理员id", required = true)
    private Long adminUserId;

    @NotNull
    @Schema(description = "模板id", required = true)
    private Long jenkinsTemplateId;

    @NotNull
    @Schema(description = "jenkins实例id", required = true)
    private Long jenkinsServiceId;

    @NotNull
    @Schema(description = "项目id", required = true)
    private Long projectId;

    @NotNull
    @Schema(description = "appid", required = true)
    private Long projectAppId;

    @NotNull
    @Schema(description = "服务组名", required = true)
    private String servicesGroupName;

    @NotNull
    @Schema(description = "启动方式", required = true)
    private Integer serviceUpdateId;

    @NotNull
    @Schema(description = "环境", required = true)
    private String serviceEnv;

    @Schema(description = "保活数量")
    private Integer serviceAliveNum = 0;

    @Schema(description = "保活检测方式")
    private Integer checkAliveType = 1;
}
