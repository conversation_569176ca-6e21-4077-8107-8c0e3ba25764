package com.mega.platform.cloud.microserviceapp.controller;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.dto.microservice.BuildServicesDTO;
import com.mega.platform.cloud.data.vo.microservice.*;
import com.mega.platform.cloud.microservice.service.ServicesService;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Test Services")
@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {

    private final ServicesService servicesService;

    @Autowired
    public TestController(ServicesService servicesService) {
        this.servicesService = servicesService;
    }

    @Operation(summary = "test")
    @PostMapping("/testBuildServicesGroup")
    public Result<?> testBuildServicesGroup(@Validated @RequestBody BuildServicesGroupReqVO vo) throws Exception {
        BuildServicesDTO dto = new BuildServicesDTO();
        BeanUtils.copyProperties(vo, dto);
        servicesService.buildServicesGroup(dto);
        return Results.success();
    }

    @Operation(summary = "test")
    @PostMapping("/createServices")
    public Result<CreateServicesRespVO> createServices(@Validated @RequestBody CreateServicesReqVO vo) throws Exception {
        CreateServicesRespVO respVO = servicesService.createServices(vo);
        return Results.success(respVO);
    }

    @Operation(summary = "test")
    @PostMapping("/createServicesGroup")
    public Result<CreateServicesGroupRespVO> createServicesGroup(@Validated @RequestBody CreateServicesGroupReqVO vo) throws Exception {
        CreateServicesGroupRespVO respVO = servicesService.createServicesGroup(vo);
        return Results.success(respVO);
    }
}
