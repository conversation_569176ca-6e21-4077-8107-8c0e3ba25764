package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 权限管理-管理员项目绑定列表响应
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-管理员项目绑定列表响应")
public class AdminAccessUserProjectBindingListRespVO {

    @Schema(description = "管理员ID")
    private Long adminUserId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "绑定状态: 0绑定, 1=解绑")
    private Integer delsign;
}
