package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "服务组查询请求参数")
public class AdminServicesGroupQueryReqVO {

    @Schema(description = "projectAppId")
    private Long projectAppId;

    @Schema(description = "服务组名称或备注（模糊查询）")
    private String keyword;

    @Schema(description = "管理员ID")
    private Long adminUserId;

    @Schema(description = "是否自研，1=自研，3=三方")
    private Integer isSelf;

    @Schema(description = "组标签ID列表 逗号分隔", example = "1,2,3")
    private String tagIds;

    @Schema(description = "上线状态，0=下线，1=上线", example = "1")
    private Integer onlineStatus;

    @Schema(description = "运行状态，0=停止，1=运行中", example = "1")
    private Integer runStatus;

    @Schema(description = "真实运行状态，0=异常，1=正常", example = "1")
    private Integer realRunStatus;
}
