package com.mega.platform.cloud.client.microservice;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.annotation.DescriptionTag;
import com.mega.platform.cloud.data.vo.microservice.BuildServicesReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesRespVO;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(
        value = "mega-cloud-microservice-" + "${spring.profiles.active}",
        contextId = "mega-cloud-microservice-services-client",
        path = "/microservice/api")
@DescriptionTag(name = "服务接口")
public interface ServicesClient {

    @Operation(summary = "创建服务")
    @PostMapping("/services/create")
    Result<CreateServicesRespVO> createServices(@Validated @RequestBody CreateServicesReqVO vo) throws Exception;

    @Operation(summary = "构建服务")
    @PostMapping("/services/build")
    Result<?> buildServices(@Validated @RequestBody BuildServicesReqVO vo) throws Exception;
}
