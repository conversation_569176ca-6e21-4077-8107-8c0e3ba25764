package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 权限管理 - 角色列表响应
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-角色列表响应")
public class AdminAccessRoleListRespVO {

    @Schema(description = "角色ID", example = "1")
    private Long adminRoleId;

    @Schema(description = "角色名称", example = "管理员")
    private String adminRoleName;

    @Schema(description = "角色描述", example = "系统管理员，拥有所有权限")
    private String adminRoleDescription;

    @Schema(description = "创建时间", example = "2025-08-15T10:00:00Z")
    private Date createTime;

    @Schema(description = "更新时间", example = "2025-08-15T10:00:00Z")
    private Date updateTime;

    @Schema(description = "删除标识: 0=未删除, 1=已删除", example = "0")
    private Integer delsign;
}
