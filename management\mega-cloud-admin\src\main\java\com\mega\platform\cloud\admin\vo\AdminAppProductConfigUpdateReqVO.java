package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(description = "应用商品配置更新请求参数")
public class AdminAppProductConfigUpdateReqVO {

    @Schema(description = "productId")
    private Long productId;

    @Schema(description = "关联的支付平台ID")
    private Long thirdPlatformId;

    @Schema(description = "外部商品ID（比如苹果平台的sku）")
    private String externalProductId;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "是否为订阅产品")
    private Boolean isSubscription;

    @Schema(description = "订阅周期（天）")
    private Integer subscriptionPeriodDay;

    @Schema(description = "商品价格")
    private BigDecimal amount;

    @Schema(description = "币种")
    private String currency;

    @Schema(description = "备注")
    private String remark;
}
