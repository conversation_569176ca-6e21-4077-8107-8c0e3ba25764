package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 全部services列表查询响应参数
 */
@Data
@Accessors(chain = true)
@Schema(name = "全部services列表查询响应参数", description = "全部services列表查询响应参数")
public class AdminServicesListRespVO {

    // services基本信息
    @Schema(description = "服务ID")
    private Long servicesId;

    @Schema(description = "服务名称")
    private String servicesName;

    @Schema(description = "服务备注")
    private String servicesRemark;

    @Schema(description = "服务状态")
    private Integer servicesStatus;

    @Schema(description = "服务运行状态")
    private Integer servicesRunningStatus;

    @Schema(description = "服务真实运行状态")
    private Integer servicesRealRunningStatus;

    @Schema(description = "服务参数列表")
    private Map<String, String> servicesParams;

    /** 组的基本信息 */
    @Schema(description = "服务组详细信息")
    private ServicesGroup servicesGroupDetail;

    @Data
    @Schema(description = "服务组信息")
    public static class ServicesGroup {
        @Schema(description = "服务组ID")
        private Long id;

        @Schema(description = "服务组名称")
        private String name;

        @Schema(description = "产品ID")
        private Long projectId;

        @Schema(description = "应用ID")
        private Long projectAppId;

        @Schema(description = "微服务更新方式")
        private Integer servicesUpdateType;

        @Schema(description = "微服务环境")
        private String servicesEnv;

        @Schema(description = "微服务日志格式id")
        private Long servicesLogFormatId;

        @Schema(description = "保活数量")
        private Integer servicesAliveNum;

        @Schema(description = "jenkins服务ID")
        private Long jenkinsServicesId;

        @Schema(description = "jenkins模板ID")
        private Long jenkinsTemplateId;

        @Schema(description = "是否自研")
        private Integer isSelf;

        @Schema(description = "负责人ID")
        private Long adminUserId;

        @Schema(description = "备注")
        private String remark;

        @Schema(description = "状态")
        private Integer status;

        @Schema(description = "运行状态")
        private Integer runningStatus;

        @Schema(description = "真实运行状态")
        private Integer realRunningStatus;

        @Schema(description = "检查运行方式")
        private Integer checkAliveType;

        @Schema(description = "是否使用jenkins")
        private Boolean useJenkins;

        @Schema(description = "创建时间")
        private Date createTime;

        @Schema(description = "更新时间")
        private Date updateTime;

        @Schema(description = "是否删除")
        private Boolean delsign;

        @Schema(description = "服务组参数列表")
        private Map<String, String> servicesGroupParams;
        // 组标签
        @Schema(description = "组标签列表")
        private List<Long> tags;
    
    }

    /** 最后一次jenkins_task_group信息 */ 
    @Schema(description = "最后一次任务组ID")
    private Long lastTaskGroupId;

    @Schema(description = "最后一次任务组操作类型")
    private Integer lastTaskGroupAction;

    @Schema(description = "最后一次任务组是否成功")
    private Integer lastTaskGroupIsSuccess;

    @Schema(description = "最后一次任务组完成时间")
    private Date lastTaskGroupCompleteTime;

    /** 最后一次jenkins_task信息 */
    @Schema(description = "最后一次任务ID")
    private Long lastTaskId;

    @Schema(description = "最后一次任务操作类型")
    private Integer lastTaskAction;

    @Schema(description = "最后一次任务是否成功")
    private Integer lastTaskIsSuccess;

    @Schema(description = "最后一次任务完成时间")
    private Date lastTaskCompleteTime;

    @Schema(description = "最后一次任务Jenkins链接")
    private String lastTaskJenkinsJobUrl;
}
