package com.mega.platform.cloud.client.monitor;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.annotation.DescriptionTag;
import com.mega.platform.cloud.data.vo.monitor.MetricsEcsCollectReqVO;
import com.mega.platform.cloud.data.vo.monitor.MetricsServicesCollectReqVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;

@FeignClient(
        value = "mega-cloud-monitor-" + "${spring.profiles.active}",
        contextId = "mega-cloud-monitor-metrics-client",
        path = "/monitor/api")
@DescriptionTag(name = "指标上报接口")
public interface MonitorMetricsClient {

    @Operation(summary = "服务器指标上报")
    @PostMapping("/metrics/ecs/collect")
    Result<?> metricsEcsCollect(@Validated @RequestBody MetricsEcsCollectReqVO reqVO) throws Exception;

    @Operation(summary = "业务指标上报")
    @PostMapping("/metrics/services/collect")
    Result<?> metricsServicesCollect(@Validated @RequestBody MetricsServicesCollectReqVO reqVO) throws Exception;
}
