package com.mega.platform.cloud.admin.vo;

import com.mega.platform.cloud.data.entity.JenkinsJobTemplate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@Schema(description = "jenkins模板列表响应数据")
public class AdminBaseJenkinsTemplateListRespVO {

    @Schema(description = "jenkins模板列表")
    private List<JenkinsJobTemplate> jenkinsJobTemplates;
}
