package com.mega.platform.cloud.data.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "AuthDouyinConfig", description = "抖音平台配置")
public class AuthDouyinConfig {

    @Schema(description = "抖音小程序 ID", example = "tt1234567890abcdef")
    private String miniAppId;

    @Schema(description = "抖音小程序 Secret", example = "abcdef1234567890")
    private String miniAppSecret;
}
