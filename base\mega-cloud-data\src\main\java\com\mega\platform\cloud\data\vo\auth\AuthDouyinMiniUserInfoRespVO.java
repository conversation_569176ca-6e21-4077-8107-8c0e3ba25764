package com.mega.platform.cloud.data.vo.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "抖音小程序用户信息返回参数")
public class AuthDouyinMiniUserInfoRespVO {

    private String openId;
    private String unionId;
    private String sessionKey;

    @Schema(description = "验证结果")
    private boolean success;
}
