package com.mega.platform.cloud.auth.client;

import com.douyin.openapi.client.Client;
import com.douyin.openapi.client.models.*;
import com.douyin.openapi.credential.models.Config;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.auth.cache.AuthAppConfigCache;
import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import com.mega.platform.cloud.data.entity.AuthDouyinConfig;
import com.mega.platform.cloud.data.entity.AuthDouyinMiniSessionResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 抖音小程序接口客户端
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DouyinVerifierClient {

    private final AuthAppConfigCache configCache;
    private final ObjectMapper objectMapper;

    /**
     * 获取抖音小程序用户信息
     */
    public AuthDouyinMiniSessionResp getMiniProgramSession(Long appId, String code) throws Exception {
        Client client = getClientByAppId(appId);
        AuthDouyinConfig config = getAuthConfig(appId);

        V2Jscode2sessionRequest request = new V2Jscode2sessionRequest()
                .setAppid(config.getMiniAppId())
                .setCode(code)
                .setSecret(config.getMiniAppSecret());

        V2Jscode2sessionResponse response = client.V2Jscode2session(request);

        if (response != null && response.getErrNo() == 0) {
            return new AuthDouyinMiniSessionResp()
                    .setSessionKey(response.getData().getSessionKey())
                    .setUnionId(response.getData().getUnionid())
                    .setOpenId(response.getData().getOpenid());
        }
        throw new RuntimeException("解析抖音用户信息响应失败：" + objectMapper.writeValueAsString(response));
    }

    /**
     * 推送订阅消息
     */
    public void pushSubscribeMessage(Long appId, String openId, String tplId, Map<String, String> data, String page) throws Exception {
        Client client = getClientByAppId(appId);
        AuthDouyinConfig config = getAuthConfig(appId);

        V1NotifyRequest request = new V1NotifyRequest()
                .setAccessToken(getGeneralAccessToken(appId))
                .setTplId(tplId)
                .setAppId(config.getMiniAppId())
                .setData(data)
                .setOpenId(openId)
                .setPage(page);

        V1NotifyResponse response = client.V1Notify(request);
        log.info("抖音推送消息结果：{}", objectMapper.writeValueAsString(response));
    }

    /**
     * 获取 AccessToken
     */
    private String getGeneralAccessToken(Long appId) throws Exception {
        Client client = getClientByAppId(appId);
        AuthDouyinConfig config = getAuthConfig(appId);

        AppsV2TokenRequest request = new AppsV2TokenRequest()
                .setAppid(config.getMiniAppId())
                .setGrantType("client_credential")
                .setSecret(config.getMiniAppSecret());

        AppsV2TokenResponse response = client.AppsV2Token(request);
        if (response != null && response.getData() != null) {
            return response.getData().getAccessToken();
        }
        throw new RuntimeException("获取抖音AccessToken失败：" + objectMapper.writeValueAsString(response));
    }

    /**
     * 从缓存获取配置
     */
    private AuthDouyinConfig getAuthConfig(Long appId) {
        return configCache.getTypedConfig(appId, ThirdPlatformEnum.DOUYIN.getCode(), AuthDouyinConfig.class);
    }

    /**
     * 获取 Douyin Client
     */
    private Client getClientByAppId(Long appId) throws Exception {
        AuthDouyinConfig config = getAuthConfig(appId);
        return new Client(new Config()
                .setClientKey(config.getMiniAppId())
                .setClientSecret(config.getMiniAppSecret()));
    }
}
