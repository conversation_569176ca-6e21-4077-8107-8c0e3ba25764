package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Map;

@Data
@Accessors(chain = true)
@Schema(name = "服务编辑请求参数")
public class AdminServicesEditReqVO {

    @Schema(description = "微服务名称")
    private String name;

    @Schema(description = "微服务组id")
    private Long serviceGroupId;

    @Schema(description = "目标服务器id（ecs_server_id）")
    private Long ecsServerId;

    @Schema(description = "Jenkins 构建参数")
    private Map<String, String> jenkinsParams;

    @Schema(description = "程序路径")
    private String path;

    @NotNull
    @Schema(description = "更新服务id", required = true)
    private Long servicesId;

    @Schema(description = "日志路径")
    private String logPath;

    @Schema(description = "日志状态")
    private Integer logStatus;

    @Schema(description = "服务描述")
    private String description;

    @Schema(description = "日志超时时间（秒）")
    private Integer logTimeoutSecond;

    @Schema(description = "排序字段")
    private Integer sort;

    @Schema(description = "备注信息")
    private String remark;
}
