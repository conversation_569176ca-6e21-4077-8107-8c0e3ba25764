package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.Map;

/**
 * 基于组的services查询响应参数
 */
@Data
@Accessors(chain = true)
@Schema(name = "基于组的services查询响应参数", description = "基于组的services查询响应参数")
public class AdminServicesListByGroupRespVO {

    // services基本信息
    @Schema(description = "服务ID")
    private Long servicesId;

    @Schema(description = "服务名称")
    private String servicesName;

    @Schema(description = "服务备注")
    private String servicesRemark;

    @Schema(description = "服务状态")
    private Integer servicesStatus;

    @Schema(description = "服务运行状态")
    private Integer servicesRunningStatus;

    @Schema(description = "服务真实运行状态")
    private Integer servicesRealRunningStatus;

    @Schema(description = "服务创建时间")
    private Date servicesCreateTime;

    @Schema(description = "服务参数列表")
    private Map<String, String> servicesParams;

    // 最后一次jenkins_task信息
    @Schema(description = "最后一次任务ID")
    private Long lastTaskId;

    @Schema(description = "最后一次任务操作类型")
    private Integer lastTaskAction;

    @Schema(description = "最后一次任务是否成功")
    private Integer lastTaskIsSuccess;

    @Schema(description = "最后一次任务完成时间")
    private Date lastTaskCompleteTime;

    @Schema(description = "最后一次任务Jenkins链接")
    private String lastTaskJenkinsJobUrl;

    @Schema(description = "最后一次任务Git提交号")
    private String lastTaskGitCommit;
}
