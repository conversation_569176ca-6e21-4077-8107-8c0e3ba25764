package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 权限管理-角色删除请求参数
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-角色删除请求参数")
public class AdminAccessRoleDeleteReqVO {

    @Schema(description = "角色ID", required = true, example = "1")
    @NotNull(message = "角色ID不能为空")
    private Long adminRoleId;

}
