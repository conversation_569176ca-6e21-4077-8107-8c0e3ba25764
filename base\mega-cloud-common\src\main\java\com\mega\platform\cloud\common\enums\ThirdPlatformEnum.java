package com.mega.platform.cloud.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ThirdPlatformEnum {
    WECHAT(1L, "WECHAT", "微信"),
    APPLE(2L, "APPLE", "苹果"),
    GOOGLE(3L, "GOO<PERSON><PERSON>", "谷歌"),
    ALIPAY(4L, "ALIPAY", "支付宝"),
    HUAWEI(5L, "HUAW<PERSON>", "华为"),
    XIAOMI(6L, "XIAOM<PERSON>", "小米"),
    DOUYIN(7L, "DOUYIN", "抖音");

    private final Long code;
    private final String platformCode;
    private final String platformName;

    public static ThirdPlatformEnum fromCode(Long code) {
        for (ThirdPlatformEnum value : ThirdPlatformEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }

    public static ThirdPlatformEnum fromPlatformCode(String platformCode) {
        for (ThirdPlatformEnum value : ThirdPlatformEnum.values()) {
            if (value.getPlatformCode().equalsIgnoreCase(platformCode)) {
                return value;
            }
        }
        return null;
    }
}
