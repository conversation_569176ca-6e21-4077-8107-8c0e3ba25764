package com.mega.platform.cloud.data.vo.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "苹果交易回调响应")
public class PaymentAppleCallbackRespVO {

    @Schema(description = "苹果交易id")
    private String transactionId;

    @Schema(description = "苹果产品id")
    private String productId;

    @Schema(description = "回调类型")
    private String type;

    @Schema(description = "回调校验结果")
    private boolean success;
}
