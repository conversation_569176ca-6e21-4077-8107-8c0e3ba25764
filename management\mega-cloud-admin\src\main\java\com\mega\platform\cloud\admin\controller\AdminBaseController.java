package com.mega.platform.cloud.admin.controller;

import com.mega.platform.cloud.admin.service.AdminBaseService;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@Tag(name = "基础接口", description = "基础接口相关API")
@Slf4j
@RequiredArgsConstructor
public class AdminBaseController {

    private final AdminBaseService adminBaseService;

    @Operation(summary = "基础接口 字典列表查询")
    @PostMapping("/public/dic/list")
    public Result<AdminBaseDicListRespVO> dicList(@RequestBody AdminBaseDicListReqVO reqVO) {
        return Results.success(adminBaseService.getDicList(reqVO));
    }

    @Operation(summary = "基础接口 日志格式列表查询")
    @PostMapping("/public/log/format/list")
    public Result<AdminBaseLogFormatListRespVO> logFormatList() {
        return Results.success(adminBaseService.getLogFormatList());
    }

    @Operation(summary = "基础接口 jenkins实例列表查询")
    @PostMapping("/public/microservice/jenkins/instances/list")
    public Result<AdminBaseJenkinsInstancesListRespVO> jenkinsInstancesList() {
        return Results.success(adminBaseService.getJenkinsInstancesList());
    }

    @Operation(summary = "基础接口 jenkins模板列表查询")
    @PostMapping("/public/microservice/jenkins/template/list")
    public Result<AdminBaseJenkinsTemplateListRespVO> jenkinsTemplateList() {
        return Results.success(adminBaseService.getJenkinsTemplateList());
    }

    @Operation(summary = "基础接口 jenkins模板参数列表查询")
    @PostMapping("/public/microservice/jenkins/param/list")
    public Result<AdminBaseJenkinsTemplateParamListRespVO> jenkinsTemplateListParam(@RequestBody AdminBaseJenkinsTemplateParamListReqVO reqVO) {
        return Results.success(adminBaseService.getJenkinsTemplateListParam(reqVO));
    }

    @Operation(summary = "基础接口 ecs_server列表列表查询 projectId=-1全部查询")
    @PostMapping("/{projectId}/microservice/ecs/server/list")
    public Result<AdminBaseEcsServerListRespVO> ecsServerList(@PathVariable Long projectId) {
        return Results.success(adminBaseService.getEcsServerList(projectId));
    }
}
