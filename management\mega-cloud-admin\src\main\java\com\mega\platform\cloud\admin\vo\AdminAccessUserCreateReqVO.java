package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 权限管理-新增管理员请求
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-新增管理员请求")
public class AdminAccessUserCreateReqVO {

    @Schema(description = "管理员用户名")
    @NotBlank(message = "用户名不能为空")
    @Size(max = 128, message = "用户名长度不能超过128个字符")
    private String adminUsername;

    @Schema(description = "密码")
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;
}
