package com.mega.platform.cloud.admin.controller;

import com.mega.platform.cloud.admin.constant.AdminAuthConstant;
import com.mega.platform.cloud.admin.service.AdminServiceGroupManageService;
import com.mega.platform.cloud.admin.service.AdminServiceManageService;
import com.mega.platform.cloud.admin.service.AdminServiceQueryService;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.entity.JenkinsTaskGroup;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@RestController
@Tag(name = "service管理")
@Slf4j
@RequiredArgsConstructor
@Validated
public class AdminServiceManageController {
    private final AdminServiceManageService adminServiceManageService;
    private final AdminServiceGroupManageService adminServiceGroupManageService;
    private final AdminServiceQueryService adminServiceQueryService;

    /**
     * 创建服务组
     */
    @Operation(summary = "创建服务组")
    @PostMapping("/{projectId}/microservice/service-group/create")
    public Result<?> createServicesGroup(@PathVariable("projectId") Long projectId,
                                         @Valid @RequestBody AdminServicesGroupCreateReqVO reqVO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        reqVO.setAdminUserId(adminUserId);
        adminServiceGroupManageService.createServicesGroup(projectId, reqVO);
        return Results.success();
    }

    /**
     * 编辑服务组
     */
    @Operation(summary = "编辑服务组")
    @PostMapping("/{projectId}/microservice/service-group/edit")
    public Result<?> editServicesGroup(@PathVariable("projectId") Long projectId,
                                       @Valid @RequestBody AdminServicesGroupEditReqVO reqVO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        reqVO.setAdminUserId(adminUserId);
        adminServiceGroupManageService.editServicesGroup(projectId, reqVO);
        return Results.success();
    }

    /**
     * 更改服务组状态
     */
    @Operation(summary = "更改服务组状态")
    @PostMapping("/{projectId}/microservice/service-group/status/edit")
    public Result<?> editServicesGroupStatus(@PathVariable("projectId") Long projectId,
                                             @Valid @RequestBody AdminServicesGroupStatusEditReqVO reqVO) {
        adminServiceGroupManageService.editServicesGroupStatus(projectId, reqVO);
        return Results.success();
    }

    /**
     * 重启服务组
     */
    @Operation(summary = "重启服务组")
    @PostMapping("/{projectId}/microservice/service-group/restart")
    public Result<?> restartServicesGroup(@PathVariable("projectId") Long projectId,
                                          @Valid @RequestBody AdminServicesGroupActionReqVO reqVO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        reqVO.setAdminUserId(adminUserId);
        adminServiceGroupManageService.restartServicesGroup(projectId, reqVO);
        return Results.success();
    }

    /**
     * 停止服务组
     */
    @Operation(summary = "停止服务组")
    @PostMapping("/{projectId}/microservice/service-group/stop")
    public Result<?> stopServicesGroup(@PathVariable("projectId") Long projectId,
                                       @Valid @RequestBody AdminServicesGroupActionReqVO reqVO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        reqVO.setAdminUserId(adminUserId);
        adminServiceGroupManageService.stopServicesGroup(projectId, reqVO);
        return Results.success();
    }

    /**
     * 删除服务组
     */
    @Operation(summary = "删除服务组")
    @PostMapping("/{projectId}/microservice/service-group/delete")
    public Result<?> deleteServicesGroup(@PathVariable("projectId") Long projectId,
                                         @Valid @RequestBody AdminServicesGroupActionReqVO reqVO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        reqVO.setAdminUserId(adminUserId);
        adminServiceGroupManageService.deleteServicesGroup(projectId, reqVO);
        return Results.success();
    }

    /**
     * 服务组列表
     */
    @Operation(summary = "服务组列表")
    @PostMapping({"/{projectId}/microservice/service/group/list", "/system/microservice/service/group/list"})
    public Result<List<AdminServicesGroupRespVO>> listServiceGroups(
            @PathVariable(value = "projectId", required = false) Long projectId,
            @Validated @RequestBody AdminServicesGroupQueryReqVO reqVO) {
        List<AdminServicesGroupRespVO> groupList = adminServiceGroupManageService.listServicesGroups(projectId, reqVO);
        return Results.success(groupList);
    }

    /**
     * 查询服务组的最后一次 Jenkins 任务记录列表
     */
    @Operation(summary = "查询服务组的最后一次 Jenkins 任务记录")
    @PostMapping("/{projectId}/microservice/job/group/last")
    public Result<JenkinsTaskGroup> getLastJenkinsTaskGroup(
            @PathVariable("projectId") Long projectId,
            @Validated @RequestBody AdminServicesGroupLastTaskReqVO reqVO) {
        JenkinsTaskGroup lastTask = adminServiceGroupManageService.lastTaskGroupByGroupId(reqVO.getServicesGroupId());
        return Results.success(lastTask);
    }

    /**
     * 正在执行的任务组列表
     */
    @Operation(summary = "正在执行的任务组列表")
    @PostMapping({"/{projectId}/microservice/job/group/current/list", "/system/microservice/job/group/current/list"})
    public Result<List<AdminServicesGroupRunningJobRespVO>> listServiceGroupsJob(
            @PathVariable(value = "projectId", required = false) Long projectId) {
        List<AdminServicesGroupRunningJobRespVO> runningJobRespVOS = adminServiceGroupManageService.listServicesJobGroups(projectId);
        return Results.success(runningJobRespVOS);
    }

    /**
     * 历史任务组列表接口
     */
    @Operation(summary = "历史任务组列表接口")
    @PostMapping({"/{projectId}/microservice/job/group/history/list", "/microservice/job/group/history/list"})
    public Result<List<AdminServicesGroupRunningJobRespVO>> listServiceGroupsJobHistory(
            @PathVariable(value = "projectId", required = false) Long projectId,
            @Validated @RequestBody AdminServicesGroupQueryReqVO reqVO) {
        List<AdminServicesGroupRunningJobRespVO> jobRespVOS = adminServiceGroupManageService.listServiceGroupsJobHistory(projectId, reqVO);
        return Results.success(jobRespVOS);
    }

    /**
     * 创建服务
     */
    @Operation(summary = "创建服务")
    @PostMapping("/{projectId}/microservice/service/create")
    public Result<?> createService(@Validated @RequestBody AdminServicesCreateReqVO reqVO,
                                   HttpServletRequest request, @PathVariable String projectId) {
        try {
            Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
            reqVO.setAdminUserId(adminUserId);
            adminServiceManageService.createService(reqVO);
            return Results.success();
        } catch (Exception e) {
            log.error("创建服务失败，projectId: {}, servicesName: {}", projectId, reqVO.getServicesName(), e);
            return Results.error(0, e.getMessage(), null);
        }
    }

    /**
     * 编辑服务
     */
    @Operation(summary = "编辑服务")
    @PostMapping("/{projectId}/microservice/service/edit")
    public Result<?> editService(@Validated @RequestBody AdminServicesEditReqVO reqVO,
                                 @PathVariable String projectId) {
        try {
            adminServiceManageService.editService(reqVO);
            return Results.success();
        } catch (Exception e) {
            log.error("编辑服务失败，servicesId: {}", reqVO.getServicesId(), e);
            return Results.error(0, e.getMessage(), null);
        }
    }

    /**
     * 上线下线服务
     */
    @Operation(summary = "上线下线服务")
    @PostMapping("/{projectId}/microservice/service/status/edit")
    public Result<?> editServiceStatus(@Validated @RequestBody AdminServicesStatusEditReqVO reqVO,
                                       @PathVariable String projectId) {
        try {
            adminServiceManageService.editServiceStatus(reqVO);
            return Results.success();
        } catch (Exception e) {
            log.error("上线下线服务失败，servicesId: {}", reqVO.getServicesId(), e);
            return Results.error(0, e.getMessage(), null);
        }
    }


    /**
     * 停止服务
     */
    @Operation(summary = "停止服务")
    @PostMapping("/{projectId}/microservice/service/status/stop")
    public Result<?> stopService(@Validated @RequestBody AdminServicesHandleReqVO reqVO, HttpServletRequest request,
                                 @PathVariable String projectId) {
        try {
            Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
            adminServiceManageService.stopService(reqVO, adminUserId);
            return Results.success();
        } catch (Exception e) {
            log.error("停止服务失败，servicesId: {}", reqVO.getServicesId(), e);
            return Results.error(0, e.getMessage(), null);
        }
    }

    /**
     * 删除服务
     */
    @Operation(summary = "删除服务")
    @PostMapping("/{projectId}/microservice/service/status/delete")
    public Result<?> deleteService(@Validated @RequestBody AdminServicesHandleReqVO reqVO, HttpServletRequest request,
                                   @PathVariable String projectId) {
        try {
            adminServiceManageService.deleteService(reqVO);
            return Results.success();
        } catch (Exception e) {
            log.error("删除服务失败，servicesId: {}", reqVO.getServicesId(), e);
            return Results.error(0, e.getMessage(), null);
        }
    }

    /**
     * 项目查询全部services列表
     */
    @Operation(summary = "项目查询全部services列表", description = "支持多种条件筛选查询services列表")
    @PostMapping("/{projectId}/microservice/service/list")
    public Result<List<AdminServicesListRespVO>> getServicesList(@PathVariable("projectId") Long projectId, @RequestBody(required = false) AdminServicesListReqVO reqVO) {
        List<AdminServicesListRespVO> result = adminServiceQueryService.getServicesList(projectId, reqVO);
        return Results.success(result);
    }

    /**
     * 系统查询全部services列表
     */
    @Operation(summary = "系统查询全部services列表", description = "支持多种条件筛选查询services列表")
    @PostMapping("/system/s-microservice/service/list")
    public Result<List<AdminServicesListRespVO>> getServicesListBySystem(@RequestBody(required = false) AdminServicesListReqVO reqVO) {
        List<AdminServicesListRespVO> result = adminServiceQueryService.getServicesList(null, reqVO);
        return Results.success(result);
    }


    /**
     * 基于组查询services列表
     */
    @Operation(summary = "基于组查询services列表", description = "查询指定服务组下的所有services")
    @PostMapping("/{projectId}/microservice/group/service/list")
    public Result<List<AdminServicesListByGroupRespVO>> getServicesListByGroup(@PathVariable("projectId") Long projectId, @RequestBody @Valid AdminServicesListByGroupReqVO reqVO) {
        List<AdminServicesListByGroupRespVO> result = adminServiceQueryService.getServicesListByGroup(projectId, reqVO);
        return Results.success(result);
    }

    /**
     * 查询服务最后一次Jenkins任务
     */
    @Operation(summary = "查询服务最后一次Jenkins任务", description = "查询指定服务的最后一次Jenkins任务执行情况")
    @PostMapping("/{projectId}/microservice/job/last")
    public Result<AdminServicesLastTaskRespVO> getServicesLastTask(@PathVariable("projectId") Long projectId, @RequestBody @Valid AdminServicesLastTaskReqVO reqVO) {
        AdminServicesLastTaskRespVO result = adminServiceQueryService.getServicesLastTask(projectId, reqVO);
        return Results.success(result);
    }

    /**
     * 查询Jenkins任务组列表
     */
    @Operation(summary = "查询Jenkins任务组列表", description = "查询指定服务组的所有Jenkins任务组列表")
    @PostMapping("/{projectId}/microservice/job/group/list")
    public Result<List<AdminJenkinsTaskGroupListRespVO>> getJenkinsTaskGroupList(@PathVariable("projectId") Long projectId, @RequestBody @Valid AdminJenkinsTaskGroupListReqVO reqVO) {
        List<AdminJenkinsTaskGroupListRespVO> result = adminServiceQueryService.getJenkinsTaskGroupList(projectId, reqVO);
        return Results.success(result);
    }

    /**
     * 查询Jenkins任务组日志
     */
    @Operation(summary = "查询Jenkins任务组日志", description = "查询指定任务组的详细日志内容")
    @PostMapping("/{projectId}/microservice/job/group/log")
    public Result<List<AdminJenkinsTaskGroupLogRespVO>> getJenkinsTaskGroupLog(@PathVariable("projectId") Long projectId, @RequestBody @Valid AdminJenkinsTaskGroupLogReqVO reqVO) {
        List<AdminJenkinsTaskGroupLogRespVO> result = adminServiceQueryService.getJenkinsTaskGroupLog(projectId, reqVO);
        return Results.success(result);
    }
}
