package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "AdminAppAuthSmsReqVO")
public class AdminAppAuthSmsReqVO {

    @Schema(description = "应用ID")
    private Long projectAppId;

    @Schema(description = "第三方通用平台配置")
    private Long thirdPlatformId;

    @Schema(description = "验证码用途类型，例如 login、register、reset_pwd、bind_phone 等")
    private String type;

    @Schema(description = "模版code 查列表时不传")
    private String smsTemplateCode;
}
