package com.mega.platform.cloud.data.vo.auth;

import com.mega.platform.cloud.data.vo.BaseReqVO;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "微信用户信息请求参数")
public class AuthWeChatUserInfoReqVO extends BaseReqVO {

    @Schema(description = "微信登录code", required = true)
    @NotBlank(message = "code不能为空")
    private String code;

    @Schema(description = "客户端IP")
    private String clientIp;

    @Schema(description = "设备ID")
    private String deviceId;
}
