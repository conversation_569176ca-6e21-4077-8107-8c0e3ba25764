package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 权限管理-角色用户绑定更新请求参数
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-角色用户绑定更新请求参数")
public class AdminAccessRoleUserBindingUpdateReqVO {

    @Schema(description = "角色ID", example = "1", required = true)
    @NotNull(message = "角色ID不能为空")
    private Long adminRoleId;

    @Schema(description = "用户ID", example = "1", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long adminUserId;

    @Schema(description = "删除标识", example = "0", required = true)
    @NotNull(message = "删除标识不能为空")
    private Integer delsign;

}
