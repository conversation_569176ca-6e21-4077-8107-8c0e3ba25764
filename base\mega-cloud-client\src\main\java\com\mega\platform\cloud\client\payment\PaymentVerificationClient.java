package com.mega.platform.cloud.client.payment;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.annotation.DescriptionTag;
import com.mega.platform.cloud.data.vo.payment.*;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.IOException;

@FeignClient(
        value = "mega-cloud-payment-" + "${spring.profiles.active}",
        contextId = "mega-cloud-payment-verification-client",
        path = "/payment/api")
@DescriptionTag(name = "支付校验接口")
public interface PaymentVerificationClient {
    @Operation(summary = "苹果交易校验")
    @PostMapping("/verification/apple/transaction")
    public Result<PaymentAppleVerifyTransactionRespVO> verifyAppleTransaction(@Validated @RequestBody PaymentAppleVerifyTransactionReqVO vo) throws IOException;

    @Operation(summary = "苹果交易回调")
    @PostMapping("/verification/apple/transaction/callback")
    public Result<PaymentAppleCallbackRespVO> appleCallback(@Validated @RequestBody PaymentAppleCallbackReqVO vo);

    @Operation(summary = "支付宝APP交易创建")
    @PostMapping("/verification/alipay/app/transaction/create")
    public Result<PaymentAlipayCreateRespVO> createAlipayTransaction(@Validated @RequestBody PaymentAlipayCreateReqVO vo);

    @Operation(summary = "支付宝交易回调")
    @PostMapping("/verification/alipay/transaction/callback")
    public Result<PaymentAlipayCallbackRespVO> alipayCallback(@Validated @RequestBody PaymentAlipayCallbackReqVO vo);

    @Operation(summary = "微信APP交易创建")
    @PostMapping("/verification/wechat/app/transaction/create")
    public Result<PaymentWeChatCreateRespVO>  createWeChatTransaction(@Validated @RequestBody PaymentWeChatCreateReqVO vo);

    @Operation(summary = "微信交易回调")
    @PostMapping("/verification/wechat/transaction/callback")
    public Result<PaymentWeChatCallbackRespVO>  wechatCallback(@Validated @RequestBody PaymentWeChatCallbackReqVO vo);
}
