package com.mega.platform.cloud.data.vo.auth;

import com.mega.platform.cloud.data.vo.BaseReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
@Schema(description = "短信验证码校验请求参数")
public class AuthVerifySmsCodeReqVO extends BaseReqVO {

    @Schema(description = "区号（如 +86）", example = "+86", required = true)
    @NotBlank(message = "区号不能为空")
    private String areaCode;

    @Schema(description = "手机号", example = "13800138000", required = true)
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1\\d{10}$", message = "手机号格式不正确")
    private String phoneNum;

    @Schema(description = "验证码", required = true)
    @NotBlank(message = "验证码不能为空")
    private String code;

    @Schema(description = "客户端IP", required = true)
    @NotBlank(message = "clientIp不能为空")
    private String clientIp;

    @Schema(description = "设备ID", required = true)
    @NotBlank(message = "deviceId不能为空")
    private String deviceId;
}
