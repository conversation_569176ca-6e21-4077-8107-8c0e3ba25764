package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "应用列表查询请求参数")
public class AdminAppListReqVO {

    @Schema(description = "页码", example = "1")
    private Integer pageNum = 1;

    @Schema(description = "页大小", example = "20")
    private Integer pageSize = 20;

    @Schema(description = "应用名称", example = " ")
    private String name;

    @Schema(description = "状态：-1全部 0不可用，1正常，2挂起，3审核中", example = "-1")
    private Integer status;
}
