package com.mega.platform.cloud.admin.controller;

import com.mega.platform.cloud.admin.service.AdminAppPermissionService;
import com.mega.platform.cloud.admin.vo.AdminAppPermissionListReqVO;
import com.mega.platform.cloud.admin.vo.AdminAppPermissionListRespVO;
import com.mega.platform.cloud.admin.vo.AdminUrlPatternListReqVO;
import com.mega.platform.cloud.admin.vo.AdminUrlPatternListRespVO;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * App访问平台权限控制器
 */
@RestController
@Tag(name = "App访问平台权限管理")
@Slf4j
public class AdminAppPermissionController {

    private final AdminAppPermissionService adminAppPermissionService;

    @Autowired
    public AdminAppPermissionController(AdminAppPermissionService adminAppPermissionService) {
        this.adminAppPermissionService = adminAppPermissionService;
    }

    /**
     * 获取路由配置列表
     *
     * @param reqVO 请求参数
     * @return 路由配置列表
     */
    @PostMapping("/system/app-permission/url-pattern-list")
    @Operation(summary = "获取路由配置列表")
    public Result<List<AdminUrlPatternListRespVO>> urlPatternList(@Validated @RequestBody AdminUrlPatternListReqVO reqVO) {
        log.info("获取路由配置列表请求");
        List<AdminUrlPatternListRespVO> result = adminAppPermissionService.urlPatternList();
        return Results.success(result);
    }

    /**
     * 获取App权限列表
     *
     * @param reqVO 请求参数
     * @return App权限列表
     */
    @PostMapping("/system/app-permission/list")
    @Operation(summary = "获取App权限列表")
    public Result<List<AdminAppPermissionListRespVO>> appPermissionList(@Validated @RequestBody AdminAppPermissionListReqVO reqVO) {
        log.info("获取App权限列表请求");
        List<AdminAppPermissionListRespVO> result = adminAppPermissionService.appPermissionList();
        return Results.success(result);
    }
}
