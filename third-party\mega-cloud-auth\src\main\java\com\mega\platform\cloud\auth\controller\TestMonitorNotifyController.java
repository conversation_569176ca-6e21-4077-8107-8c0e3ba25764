package com.mega.platform.cloud.auth.controller;

import com.mega.platform.cloud.common.utils.JwtTokenUtil;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/actuator/platform")
@Tag(name = "测试导流通知接口", description = "平台监控相关测试通知接口")
public class TestMonitorNotifyController {
    private final JwtTokenUtil jwtTokenUtil;

    public TestMonitorNotifyController(JwtTokenUtil jwtTokenUtil) {
        this.jwtTokenUtil = jwtTokenUtil;
    }

    @Operation(summary = "测试停止通知接口")
    @PostMapping("/notify/stop")
    public Result<?> platformNotifyStop() {
        return Results.success();
    }

    @Operation(summary = "测试是否能停止接口")
    @PostMapping("/check/canStop")
    public Result<?> platformCheckCanStop() {
        return Results.success();
    }
}
