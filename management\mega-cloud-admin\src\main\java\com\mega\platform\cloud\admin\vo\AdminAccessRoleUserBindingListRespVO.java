package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 权限管理-角色用户绑定列表响应参数
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-角色用户绑定列表响应参数")
public class AdminAccessRoleUserBindingListRespVO {

    @Schema(description = "角色ID", example = "1")
    private Long adminRoleId;

    @Schema(description = "用户ID", example = "1")
    private Long adminUserId;

    @Schema(description = "用户名", example = "admin")
    private String adminUserName;

    @Schema(description = "删除标识(0=正常,1=删除)", example = "0")
    private Integer delsign;
}
