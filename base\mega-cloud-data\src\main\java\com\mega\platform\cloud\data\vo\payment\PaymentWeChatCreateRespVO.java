package com.mega.platform.cloud.data.vo.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "微信支付创建响应参数")
public class PaymentWeChatCreateRespVO {

    @Schema(description = "是否成功")
    private boolean success;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "商户订单号")
    private String outTradeNo;

    @Schema(description = "预支付ID")
    private String prepayId;
}
