package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 权限管理-删除管理员请求
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-删除管理员请求")
public class AdminAccessUserDeleteReqVO {

    @NotNull(message = "管理员ID不能为空")
    @Schema(description = "管理员ID", required = true)
    private Long adminUserId;
}
