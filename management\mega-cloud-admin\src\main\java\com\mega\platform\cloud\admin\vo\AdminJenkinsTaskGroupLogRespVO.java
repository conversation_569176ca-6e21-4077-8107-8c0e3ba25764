package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 查询Jenkins任务组日志响应参数
 */
@Data
@Accessors(chain = true)
@Schema(name = "AdminJenkinsTaskGroupLogRespVO", description = "查询Jenkins任务组日志响应参数")
public class AdminJenkinsTaskGroupLogRespVO {

    @Schema(description = "任务组ID")
    private Long jenkinsTaskGroupId;

    @Schema(description = "任务ID")
    private Long jenkinsTaskId;

    @Schema(description = "日志内容列表")
    private List<String> logContentList;
}
