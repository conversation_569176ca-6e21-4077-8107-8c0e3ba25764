package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 查询Jenkins任务组列表响应参数
 */
@Data
@Accessors(chain = true)
@Schema(name = "查询Jenkins任务组列表响应参数", description = "查询Jenkins任务组列表响应参数")
public class AdminJenkinsTaskGroupListRespVO {

    @Schema(description = "任务组ID")
    private Long id;

    @Schema(description = "操作用户ID")
    private Long adminUserId;

    @Schema(description = "微服务组ID")
    private Long servicesGroupId;

    @Schema(description = "操作类型")
    private Integer action;

    @Schema(description = "操作参数或请求详情")
    private String requestData;

    @Schema(description = "任务数量")
    private Integer taskNum;

    @Schema(description = "是否成功")
    private Byte isSuccess;

    @Schema(description = "失败原因")
    private String failedReason;

    @Schema(description = "执行完成时间")
    private Date completeTime;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "逻辑删除标志")
    private Byte delsign;
}
