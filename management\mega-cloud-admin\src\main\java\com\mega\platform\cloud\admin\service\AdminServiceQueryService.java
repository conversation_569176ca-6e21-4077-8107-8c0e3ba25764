package com.mega.platform.cloud.admin.service;

import com.mega.platform.cloud.admin.dao.AdminServiceQueryDao;
import com.mega.platform.cloud.admin.dto.AdminServicesJenkinsParamDTO;
import com.mega.platform.cloud.admin.dto.AdminServicesTagDTO;
import com.mega.platform.cloud.admin.dto.AdminJenkinsTaskLogDTO;
import com.mega.platform.cloud.admin.dto.AdminServicesDetailDTO;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.AdminErrorCode;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 服务查询业务逻辑层
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdminServiceQueryService {

    
    private final AdminServiceQueryDao adminServiceQueryDao;

    /**
     * 查询全部services列表
     * @param projectId 项目ID
     * @param reqVO 查询条件
     * @return services列表
     */
    public List<AdminServicesListRespVO> getServicesList(Long projectId, AdminServicesListReqVO reqVO) {

            // 1 查询总信息
            List<AdminServicesDetailDTO> result = adminServiceQueryDao.selectServicesList(projectId, reqVO);
            if (result == null || result.isEmpty()) {
                return new ArrayList<>();
            }
            log.info("查询全部services列表成功，projectId: {}, 结果数量: {}", projectId, result.size());

            // 遍历result，把所有servicesId存入Set<Long> ,也把servicesGroupId存入Set<Long>
            Set<Long> servicesIds = new HashSet<>();
            Set<Long> servicesGroupIds = new HashSet<>();
            List<AdminServicesListRespVO> respVOs = new ArrayList<>();
            for (AdminServicesDetailDTO item : result) {
                servicesIds.add(item.getServicesId());
                servicesGroupIds.add(item.getId());

                AdminServicesListRespVO respVO = new AdminServicesListRespVO();

                // 设置services基本信息
                respVO.setServicesId(item.getServicesId());
                respVO.setServicesName(item.getServicesName());
                respVO.setServicesRemark(item.getServicesRemark());
                respVO.setServicesStatus(item.getServicesStatus());
                respVO.setServicesRunningStatus(item.getServicesRunningStatus());
                respVO.setServicesRealRunningStatus(item.getServicesRealRunningStatus());

                // 设置服务组信息
                AdminServicesListRespVO.ServicesGroup group = new AdminServicesListRespVO.ServicesGroup();
                group.setId(item.getId());
                group.setName(item.getName());
                group.setProjectId(item.getProject_id());
                group.setProjectAppId(item.getProject_app_id());
                group.setServicesUpdateType(item.getServices_update_type());
                group.setServicesEnv(item.getServices_env());
                group.setServicesLogFormatId(item.getServices_log_format_id());
                group.setServicesAliveNum(item.getServices_alive_num());
                group.setJenkinsServicesId(item.getJenkins_services_id());
                group.setJenkinsTemplateId(item.getJenkins_template_id());
                group.setIsSelf(item.getIs_self());
                group.setAdminUserId(item.getAdmin_user_id());
                group.setRemark(item.getRemark());
                group.setStatus(item.getStatus());
                group.setRunningStatus(item.getRunning_status());
                group.setRealRunningStatus(item.getReal_running_status());
                group.setCheckAliveType(item.getCheck_alive_type());
                group.setUseJenkins(item.getUse_jenkins() != null && item.getUse_jenkins() == 1);
                group.setCreateTime(item.getCreate_time());
                group.setUpdateTime(item.getUpdate_time());
                group.setDelsign(item.getDelsign() != null && item.getDelsign() == 1);
                respVO.setServicesGroupDetail(group);

                // 设置最后一次jenkins任务组信息
                respVO.setLastTaskGroupId(item.getLastTaskGroupId());
                respVO.setLastTaskGroupAction(item.getLastTaskGroupAction());
                respVO.setLastTaskGroupIsSuccess(item.getLastTaskGroupIsSuccess());
                respVO.setLastTaskGroupCompleteTime(item.getLastTaskGroupCompleteTime());

                respVOs.add(respVO);
            }

            // 2 查询tags
            Map<Long, List<Long>> servicesTagsMap = getServicesTagsMap(servicesGroupIds);

            // 3 查询services params 
            Map<Long, Map<String,String>> servicesParamsMap = getServicesParams(servicesIds);

            // 4 查询services group params
            Map<Long, Map<String,String>> servicesGroupParamsMap = getServicesGroupParams(servicesGroupIds);
            
            // 遍历respVOs补充tags和params信息
            for (AdminServicesListRespVO item : respVOs) {   
                Map<String,String> servicesParams = servicesParamsMap.get(item.getServicesId());
                item.setServicesParams(servicesParams != null ? servicesParams : new HashMap<>());

                List<Long> tags = servicesTagsMap.get(item.getServicesGroupDetail().getId());
                item.getServicesGroupDetail().setTags(tags != null ? tags : new ArrayList<>());
                
                Map<String,String> groupParams = servicesGroupParamsMap.get(item.getServicesGroupDetail().getId());
                item.getServicesGroupDetail().setServicesGroupParams(groupParams != null ? groupParams : new HashMap<>());
            }

            return respVOs;
    }
            

    /**
     * 获取services标签map
     * @param servicesGroupIds
     * @return
     */
    private Map<Long, List<Long>> getServicesTagsMap( Set<Long> servicesGroupIds) {
        List<AdminServicesTagDTO> tagList = adminServiceQueryDao.selectServicesTags(servicesGroupIds);
        // 生成map key为 servicesGroupId, value为tagId列表
        return tagList.stream()
                      .collect(
                        Collectors.groupingBy(AdminServicesTagDTO::getServiceGroupId, 
                        Collectors.mapping(AdminServicesTagDTO::getTagId, Collectors.toList()))
                        ); 
    }

    private Map<Long, Map<String,String>> getServicesParams(Set<Long> servicesIds){
        List<AdminServicesJenkinsParamDTO> servicesParams = adminServiceQueryDao.selectServicesParams(servicesIds);
        // 生成map key为servicesId , value为paramKey和paramValue组成的map
        return servicesParams.stream()
                      .collect(
                        Collectors.groupingBy(AdminServicesJenkinsParamDTO::getId, 
                        Collectors.toMap(AdminServicesJenkinsParamDTO::getParamKey, AdminServicesJenkinsParamDTO::getParamValue))
                        );
    }

    private Map<Long, Map<String, String>> getServicesGroupParams(Set<Long> servicesGroupIds) {
        List<AdminServicesJenkinsParamDTO> servicesGroupParams = adminServiceQueryDao.selectServicesGroupParams(servicesGroupIds);
        // 生成map key为servicesGroupId , value为paramKey和paramValue组成的map
        return servicesGroupParams.stream()
                      .collect(
                        Collectors.groupingBy(AdminServicesJenkinsParamDTO::getId, 
                        Collectors.toMap(AdminServicesJenkinsParamDTO::getParamKey, AdminServicesJenkinsParamDTO::getParamValue))
                        );
    }


    /**
     * 基于组查询services列表
     * @param projectId 项目ID
     * @param reqVO 查询条件
     * @return services列表
     */
    public List<AdminServicesListByGroupRespVO> getServicesListByGroup(Long projectId, AdminServicesListByGroupReqVO reqVO) {
        List<AdminServicesListByGroupRespVO> result = adminServiceQueryDao.selectServicesListByGroup(projectId, reqVO.getServicesGroupId());
        if(result == null || result.isEmpty()){
            return new ArrayList<>();
        }
        Set<Long> servicesIds = new HashSet<>();
        for (AdminServicesListByGroupRespVO item : result) {
            servicesIds.add(item.getServicesId());
        }

        Map<Long, Map<String,String>> servicesParamsMap = getServicesParams(servicesIds);
        for (AdminServicesListByGroupRespVO item : result) {   
            Map<String,String> servicesParams = servicesParamsMap.get(item.getServicesId());
            item.setServicesParams(servicesParams != null ? servicesParams : new HashMap<>());
        }

        log.info("基于组查询services列表成功，projectId: {}, servicesGroupId: {}, 结果数量: {}",
                    projectId, reqVO.getServicesGroupId(), result.size());
        return result;

    }

    /**
     * 查询服务最后一次任务
     * @param projectId 项目ID
     * @param reqVO 查询条件
     * @return 服务最后一次任务信息
     */
    public AdminServicesLastTaskRespVO getServicesLastTask(Long projectId, AdminServicesLastTaskReqVO reqVO) {
        AdminServicesLastTaskRespVO result = adminServiceQueryDao.selectServicesLastTask(projectId, reqVO.getServicesId());
        log.info("查询服务最后一次任务成功，projectId: {}, servicesId: {}", projectId, reqVO.getServicesId());
        return result;
    }

    /**
     * 查询Jenkins任务组列表
     * @param projectId 项目ID
     * @param reqVO 查询条件
     * @return Jenkins任务组列表
     */
    public List<AdminJenkinsTaskGroupListRespVO> getJenkinsTaskGroupList(Long projectId, AdminJenkinsTaskGroupListReqVO reqVO) {
        List<AdminJenkinsTaskGroupListRespVO> result = adminServiceQueryDao.selectJenkinsTaskGroupList(projectId, reqVO.getServicesGroupId());
        if (result == null) {
            result = new ArrayList<>();
        }
        log.info("查询Jenkins任务组列表成功，projectId: {}, servicesGroupId: {}, 结果数量: {}",
                    projectId, reqVO.getServicesGroupId(), result.size());
        return result;
    }

    /**
     * 查询Jenkins任务组日志
     * @param projectId 项目ID
     * @param reqVO 查询条件
     * @return Jenkins任务组日志信息
     */
    public List<AdminJenkinsTaskGroupLogRespVO> getJenkinsTaskGroupLog(Long projectId, AdminJenkinsTaskGroupLogReqVO reqVO) {
        List<AdminJenkinsTaskLogDTO> rawResult = adminServiceQueryDao.selectJenkinsTaskGroupLog(projectId, reqVO.getJenkinsTaskGroupId());
        if (rawResult == null || rawResult.isEmpty()) {
            log.info("查询Jenkins任务组日志成功，projectId: {}, jenkinsTaskGroupId: {}, 结果数量: 0",
                        projectId, reqVO.getJenkinsTaskGroupId());
            return new ArrayList<>();
        }
        List<AdminJenkinsTaskGroupLogRespVO> result = new ArrayList<>();
        
        // 按jenkinsTaskId分组，将相同taskId的日志内容合并到一个List<String>中
        Map<Long, List<AdminJenkinsTaskLogDTO>> groupedLogs = new HashMap<>();
        for (AdminJenkinsTaskLogDTO log : rawResult) {
            if (log.getJenkinsTaskId() == null) {
                // 如果taskId为null，直接创建一个新的日志对象并添加到结果中
                AdminJenkinsTaskGroupLogRespVO nullTaskLog = new AdminJenkinsTaskGroupLogRespVO();
                nullTaskLog.setJenkinsTaskGroupId(reqVO.getJenkinsTaskGroupId());
                nullTaskLog.setLogContentList(List.of(log.getLogContent()));
                result.add(nullTaskLog);
                continue;
            }
            groupedLogs.computeIfAbsent(log.getJenkinsTaskId(), k -> new ArrayList<>()).add(log);
        }

        
        for (Map.Entry<Long, List<AdminJenkinsTaskLogDTO>> entry : groupedLogs.entrySet()) {
            Long taskId = entry.getKey();
            List<AdminJenkinsTaskLogDTO> logs = entry.getValue();

            // 创建合并后的日志对象
            AdminJenkinsTaskGroupLogRespVO mergedLog = new AdminJenkinsTaskGroupLogRespVO();
            mergedLog.setJenkinsTaskGroupId(reqVO.getJenkinsTaskGroupId());
            mergedLog.setJenkinsTaskId(taskId);

            // 从原始查询结果中提取日志内容，每条记录包含一个log_content
            List<String> logContentList = logs.stream()
                    .map(AdminJenkinsTaskLogDTO::getLogContent)
                    .filter(content -> content != null && !content.trim().isEmpty())
                    .collect(Collectors.toList());

            mergedLog.setLogContentList(logContentList);
            result.add(mergedLog);
        }

        log.info("查询Jenkins任务组日志成功，projectId: {}, jenkinsTaskGroupId: {}, 任务数量: {}",
                    projectId, reqVO.getJenkinsTaskGroupId(), result.size());
        return result;
    }
}