package com.mega.platform.cloud.data.vo.payment;

import com.mega.platform.cloud.data.vo.BaseReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "微信支付回调请求参数")
public class PaymentWeChatCallbackReqVO extends BaseReqVO {

    @Schema(description = "微信支付证书序列号")
    private String wechatpaySerial;

    @Schema(description = "微信支付签名")
    private String wechatpaySignature;

    @Schema(description = "微信支付时间戳")
    private String wechatpayTimestamp;

    @Schema(description = "微信支付随机串")
    private String wechatpayNonce;

    @Schema(description = "请求体内容")
    private String body;
}
