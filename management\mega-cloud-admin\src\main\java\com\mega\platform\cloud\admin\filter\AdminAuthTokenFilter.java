package com.mega.platform.cloud.admin.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.AdminErrorCode;
import com.mega.platform.cloud.admin.constant.AdminAuthConstant;
import com.mega.platform.cloud.admin.dto.AdminTokenPayload;
import com.mega.platform.cloud.admin.service.AdminAccessCacheService;
import com.mega.platform.cloud.admin.service.AdminAuthService;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.io.OutputStream;

/**
 * 管理员认证Token过滤器
 * 实现JWT token验证和权限控制
 *
 * <AUTHOR>
 */
@Component("adminAuthTokenFilter")
@Order(1)
@Slf4j
public class AdminAuthTokenFilter implements Filter {

    private final AdminAccessCacheService adminAccessCacheService;
    private final String active;

    // 路由匹配模式
    private static final String ADMIN_API_PREFIX = "/admin/api/";
    private static final String SWAGGER_PATH_PREFIX = "/admin/api/swagger-ui";
    private static final String SWAGGER_DOCS_PATH_PREFIX = "/admin/api/v3/api-docs";
    private static final String PING_PATH_PREFIX = "/admin/api/ping";
    private static final String PUBLIC_PATH_PREFIX = "/admin/api/public";
    private static final String SYSTEM_PATH_PREFIX = "/admin/api/system";
    private static final String SYSTEM_AUTH_PATH_PREFIX = "/admin/api/system/auth";
    private static final Pattern PROJECT_PATH_PATTERN = Pattern.compile("^/admin/api/(\\d+)/(.+)$");

    @Autowired
    public AdminAuthTokenFilter(AdminAccessCacheService adminAccessCacheService, @Value("${spring.profiles.active}") String active) {
        this.adminAccessCacheService = adminAccessCacheService;
        this.active = active;
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("AdminAuthTokenFilter initialized");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        String requestURI = httpRequest.getRequestURI();
        log.info("AdminAuthTokenFilter doFilter URI: {}", requestURI);
        String method = httpRequest.getMethod();
        
        try {

            //swagger
            if (requestURI.startsWith(SWAGGER_PATH_PREFIX) || requestURI.startsWith(SWAGGER_DOCS_PATH_PREFIX) || requestURI.startsWith(PING_PATH_PREFIX)) {
                chain.doFilter(request, response);
                return;
            }

            // 1. 检查是否为admin api请求
            if (!requestURI.startsWith(ADMIN_API_PREFIX)) {
                chain.doFilter(request, response);
                return;
            }
            
            // 2. OPTIONS请求直接放行
            if ("OPTIONS".equalsIgnoreCase(method)) {
                chain.doFilter(request, response);
                return;
            }

            // 判断当前环境是否为dev,如果是dev环境则直接放行
//           if ("dev".equals(active) && !"/admin/api/system/auth/profile".equals(requestURI)) {
//               chain.doFilter(request, response);
//               return;
//           }
//
            // 3. 判断路由类型并进行相应的验证
            if (requestURI.startsWith(PUBLIC_PATH_PREFIX)) {
                // public路径直接放行
                log.debug("Public path access: {}", requestURI);
                chain.doFilter(request, response);
                return;
            }
            
            // 4. 获取并验证token
            String token = extractToken(httpRequest);
            if (!StringUtils.hasText(token)) {
                sendErrorResponse(httpResponse, AdminErrorCode.TOKEN_EXPIRED, "Token缺失");
                return;
            }
            
            // 5. 验证token并获取payload
            AdminTokenPayload payload = adminAccessCacheService.validateToken(token);
            if (payload == null || payload.getAdminUserId() == null) {
                sendErrorResponse(httpResponse, AdminErrorCode.TOKEN_EXPIRED, "Token无效或已过期");
                return;
            }
            
            // 6. 将adminUserId存入request attribute
            httpRequest.setAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID, payload.getAdminUserId());
            // 超级管理员无需校验权限
            if(payload.getRoleIds() != null && payload.getRoleIds().size() > 0 && payload.getRoleIds().contains(1L)) {
                chain.doFilter(request, response);
                return;
            }
            
            // 7. 系统权限 只验证功能权限
            if (requestURI.startsWith(SYSTEM_PATH_PREFIX)) {
                // auth 只校验token
                if(requestURI.startsWith(SYSTEM_AUTH_PATH_PREFIX)) {
                    chain.doFilter(request, response);
                    return;
                }
                
                // 验证功能权限
                Pattern controllerPattern = Pattern.compile("/admin/api/system/([^/]+).*");
                Matcher controllerMatcher = controllerPattern.matcher(requestURI);
                String controller = controllerMatcher.matches() ? "/" + controllerMatcher.group(1) : requestURI.substring(SYSTEM_PATH_PREFIX.length());
                // 验证功能权限
                if (!hasFunctionPermission(payload, controller)) {
                    sendErrorResponse(httpResponse, AdminErrorCode.ACCESS_DENIED, "没有功能访问权限");
                    return;
                }

                chain.doFilter(request, response);
                return;
            }
            
            // 8. 项目路径权限验证
            Matcher matcher = PROJECT_PATH_PATTERN.matcher(requestURI);
            if (matcher.matches()) {
                String projectIdStr = matcher.group(1);
                String controller = matcher.group(2);
                
                try {
                    Long projectId = Long.parseLong(projectIdStr);
                    
                    // 验证项目权限
                    if (!hasProjectPermission(payload, projectId)) {
                        sendErrorResponse(httpResponse, AdminErrorCode.ACCESS_DENIED, "没有项目访问权限");
                        return;
                    }
                    
                    // 验证功能权限
                    if (!hasFunctionPermission(payload, controller)) {
                        sendErrorResponse(httpResponse, AdminErrorCode.ACCESS_DENIED, "没有功能访问权限");
                        return;
                    }
                
                    chain.doFilter(request, response);
                    return;
                    
                } catch (NumberFormatException e) {
                    sendErrorResponse(httpResponse, AdminErrorCode.ACCESS_DENIED, "无效的项目ID");
                    return;
                }
            }
            
            // 9. 其他路径默认拒绝访问
            sendErrorResponse(httpResponse, AdminErrorCode.ACCESS_DENIED, "无效的访问路径");
            
        } catch (Exception e) {
            log.error("Filter processing error for path: {}", requestURI, e);
            sendErrorResponse(httpResponse, AdminErrorCode.ERR_0, "系统内部错误");
        }
    }



    
    @Override
    public void destroy() {
        log.info("AdminAuthTokenFilter destroyed");
    }
    
    /**
     * 从请求头中提取token
     */
    private String extractToken(HttpServletRequest request) {
        String adminToken = request.getHeader("adminToken");
        
        if (StringUtils.hasText(adminToken)) {
            // 验证token格式是否为JWT格式
            if (adminToken.matches("^[A-Za-z0-9\\-_]+\\.[A-Za-z0-9\\-_]+\\.[A-Za-z0-9\\-_]+$")) {
                return adminToken;
            }
        } else {
            log.warn("未找到adminToken头");
        }
        return null;
    }
    
    /**
     * 检查项目权限
     */
    private boolean hasProjectPermission(AdminTokenPayload payload, Long projectId) {
        if (CollectionUtils.isEmpty(payload.getProjectIds())) {
            return false;
        }
        return payload.getProjectIds().contains(projectId);
    }
    
    /**
     * 检查功能权限
     */
    private boolean hasFunctionPermission(AdminTokenPayload payload, String controller) {
        if (CollectionUtils.isEmpty(payload.getBackendPaths())) {
            return false;
        }
        
        // 精确匹配或通配符匹配
        for (String permission : payload.getBackendPaths()) {
            // 权限匹配逻辑
            if (permission.equals("/"+controller) || permission.startsWith("/"+controller)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 发送错误响应
     */
    private void sendErrorResponse(HttpServletResponse response, AdminErrorCode errorCode, String message) 
            throws IOException {
        response.setContentType("application/json; charset=utf-8");
        response.setCharacterEncoding("UTF-8");
        Map<String, Object> errorMap = new HashMap<>();
        errorMap.put("code", errorCode.getCode());
        errorMap.put("message", message);
        String errorStr = new JSONObject(errorMap).toJSONString();
        OutputStream out = response.getOutputStream();
        out.write(errorStr.getBytes("UTF-8"));
        out.flush();
    }
}