package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 权限管理-角色路由绑定更新请求参数
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-角色路由绑定更新请求参数")
public class AdminAccessRoleRouteBindingUpdateReqVO {

    @Schema(description = "角色ID", example = "1")
    @NotNull(message = "角色ID不能为空")
    private Long roleId;

    @Schema(description = "路由ID列表", example = "[1,2,3]")
    @NotNull(message = "路由ID列表不能为空")
    private List<Long> routerIds;

}
