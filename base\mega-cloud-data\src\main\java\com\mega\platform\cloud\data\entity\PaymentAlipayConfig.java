package com.mega.platform.cloud.data.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "PaymentAlipayConfig", description = "支付宝支付平台配置")
public class PaymentAlipayConfig {

    @Schema(description = "支付宝应用 AppID", example = "2016092600591234")
    private String appId;

    @Schema(description = "支付宝公钥", example = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A...")
    private String aliPayPublicKey;

    @Schema(description = "商户私钥", example = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBK...")
    private String privateKey;

    @Schema(description = "支付成功回调地址", example = "https://example.com/alipay/callback")
    private String callbackUrl;
}
