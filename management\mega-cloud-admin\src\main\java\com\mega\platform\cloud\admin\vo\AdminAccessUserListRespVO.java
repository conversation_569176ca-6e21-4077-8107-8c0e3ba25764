package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 权限管理-管理员列表响应
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-管理员列表响应")
public class AdminAccessUserListRespVO {

    @Schema(description = "管理员ID")
    private Long id;

    @Schema(description = "管理员用户名")
    private String username;

    @Schema(description = "最后登录时间")
    private Date lastLoginTime;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "删除标识: 0=未删除, 1=已删除")
    private Integer delsign;
}
