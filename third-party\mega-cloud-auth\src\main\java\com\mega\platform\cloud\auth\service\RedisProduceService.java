package com.mega.platform.cloud.auth.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.common.CommonConfig;
import com.mega.platform.cloud.data.entity.AuthVerifyLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class RedisProduceService {
    private final StringRedisTemplate stringRedisTemplate;
    public static final String AUTH_VERIFY_LOG = "auth_verify_log_";
    private ObjectMapper objectMapper = new ObjectMapper();

    public void pushAuthVerifyLog(AuthVerifyLog authVerifyLog) {
        try {
            String value = objectMapper.writeValueAsString(authVerifyLog);
            this.stringRedisTemplate.opsForList().leftPush(AUTH_VERIFY_LOG + CommonConfig.ENV, value);
            log.info("推送{}队列完成={}", AUTH_VERIFY_LOG + CommonConfig.ENV, value);
        } catch (Exception e) {
            log.error("推送{}队列错误={}", AUTH_VERIFY_LOG + CommonConfig.ENV, e.getMessage());
        }
    }
}
