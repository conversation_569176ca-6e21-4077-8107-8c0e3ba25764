package com.mega.platform.cloud.admin.controller;

import com.mega.platform.cloud.common.mapper.ThirdPlatformMapper;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.entity.ThirdPlatform;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "三方平台管理", description = "三方平台管理相关接口")
@RequiredArgsConstructor
@RestController
@RequestMapping("/public/third/platform")
public class AdminThirdPlatformController {
    private final ThirdPlatformMapper thirdPlatformMapper;

    @Operation(summary = "Payment配置管理 获取支持的平台列表（如微信、苹果等）")
    @PostMapping("/list")
    public Result<List<ThirdPlatform>> listSupportedPlatforms() {
        List<ThirdPlatform> configs = thirdPlatformMapper.select(new ThirdPlatform().setDelsign((byte) 0));
        return Results.success(configs);
    }
}
