package com.mega.platform.cloud.data.vo.monitor;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mega.platform.cloud.data.dto.monitor.MetricsDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
@Schema(description = "ECS指标采集请求参数")
public class MetricsEcsCollectReqVO {

    @Schema(description = "指标列表", required = true)
    @NotNull
    private List<MetricsDTO> metrics;

    @Schema(description = "上报时间", required = true, example = "2025-08-15 10:30:00")
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date collectTime;
}
