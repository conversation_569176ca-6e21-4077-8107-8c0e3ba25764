api:
  docs:
    apis-package: "com.mega.platform.cloud.admin.controller"
    title: "Mega Platform Admin"
    version: 1.0.1
    description: "美嘉平台后台管理系统API文档 mega-cloud-admin"
logging:
  level:
    org.zalando.logbook: trace
    com.mega.platform: info
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %p [%X{traceId:--}] [%t] %logger{40} : %m%n"
logbook:
  format:
    style: http
  include:
    - /admin/**
  write:
    max-body-size: 1024
server:
  port: 8081
  servlet:
    context-path: /admin/api
    encoding:
      enabled: true
      charset: UTF-8
      force: true
  undertow:
    url-charset: UTF-8
spring:
  config:
    import: classpath:application-common-${spring.profiles.active}.yml
  profiles:
    active: dev
  application:
    name: mega-cloud-admin-${spring.profiles.active}
  messages:
    encoding: UTF-8
    basename: i18n/messages
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  jackson:
    default-property-inclusion: non_null
    deserialization:
      fail_on_unknown_properties: false
mybatis:
  configuration:
    map-underscore-to-camel-case: true
management:
  endpoints:
    base-path: /actuator
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
feign:
  httpclient:
    enabled: false
  okhttp:
    enabled: true
sentry:
  dsn: http://7c8a97af00174beda7907f05a114a981@**************:9000/2
  environment: ${spring.profiles.active}

# Swagger配置
springfox:
  documentation:
    swagger-ui:
      enabled: true
    swagger:
      v2:
        path: /v2/api-docs
    open-api:
      v3:
        path: /v3/api-docs
