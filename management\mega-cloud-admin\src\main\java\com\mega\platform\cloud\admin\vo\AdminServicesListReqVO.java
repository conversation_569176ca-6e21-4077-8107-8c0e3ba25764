package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 全部services列表查询请求参数
 */
@Data
@Accessors(chain = true)
@Schema(name = "全部services列表查询请求参数", description = "全部services列表查询请求参数")
public class AdminServicesListReqVO {

    @Schema(description = "服务组ID，可选的，不为空拼接到sql")
    private Long servicesGroupId;

    @Schema(description = "服务名称，可选的，不为空拼接到sql, 模糊查询")
    private String servicesName;

    @Schema(description = "备注，可选的，不为空拼接到sql, 模糊查询")
    private String remark;

    @Schema(description = "管理员，可选的，不为空拼接到sql")
    private Long adminUserId;

    @Schema(description = "组标签选择（多选）")
    private List<Long> tags;

    @Schema(description = "上线状态, 0：下线 1：上线 可选的，不为空拼接到sql")
    private Integer status;

    @Schema(description = "运行状态, 0未运行 1运行中 2构建中 3队列中 4待关闭 -1构建失败，不为空拼接到sql")
    private Integer runningStatus;

    @Schema(description = "真实运行状态：0未运行 1运行中 2构建中 3队列中 4待关闭 -1构建失败")
    private Integer realRunningStatus;
}
