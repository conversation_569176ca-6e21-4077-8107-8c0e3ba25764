package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@Schema(name = "查询服务组最后一次任务请求参数", description = "查询服务组最后一次任务请求参数")
public class AdminServicesGroupLastTaskReqVO {

    @NotNull(message = "服务组ID不能为空")
    @Schema(description = "服务组ID", required = true, example = "1")
    private Long servicesGroupId;
}
