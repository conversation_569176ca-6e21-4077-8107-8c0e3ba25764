package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 权限管理-路由列表请求参数
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-路由列表请求参数")
public class AdminAccessRouterListReqVO {

    /*
    @Schema(description = "页码", example = "1")
    private Integer pageNum = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
    */

    @Schema(description = "后端路由路径", example = "/admin/api")
    private String backendPath;

    @Schema(description = "前端路由路径", example = "/dashboard")
    private String frontendPath;

    @Schema(description = "前端路由名称", example = "Dashboard")
    private String frontendName;

    @Schema(description = "路由描述", example = "仪表板")
    private String description;

    @Schema(description = "父路由ID", example = "1")
    private Long parentAdminRouterId;

    @Schema(description = "删除标识", example = "0")
    private Integer delsign = 0;
}
