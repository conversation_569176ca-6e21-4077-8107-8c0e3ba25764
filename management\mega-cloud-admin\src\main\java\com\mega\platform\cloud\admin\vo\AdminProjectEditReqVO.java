package com.mega.platform.cloud.admin.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 项目更新请求参数
 */
@Data
@Accessors(chain = true)
@Schema(name = "AdminProjectEditReqVO", description = "项目更新请求参数")
public class AdminProjectEditReqVO {

    @NotBlank(message = "项目名称不能为空")
    @Schema(description = "项目名称")
    private String name;

    @NotNull(message = "状态不能为空")
    @Schema(description = "状态 0：不可用 1：正常 2：挂起 3：审核中")
    private Integer status;

    @Schema(description = "描述")
    private String remark;
}
