package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@Schema(description = "路由配置列表响应数据")
public class AdminUrlPatternListRespVO {

    @Schema(description = "AppID", example = "1")
    private Integer id;

    @Schema(description = "路由名称", example = "auth路由")
    private String name;

    @Schema(description = "Ant风格路径匹配", example = "/api/v1/user/**")
    private String urlPattern;

    @Schema(description = "描述")
    private String remark;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "删除标识(0=未删除,1=已删除)", example = "0")
    private Integer delsign;
}
