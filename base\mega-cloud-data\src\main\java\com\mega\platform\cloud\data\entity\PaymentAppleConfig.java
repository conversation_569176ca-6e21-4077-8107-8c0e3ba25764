package com.mega.platform.cloud.data.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "PaymentAppleConfig", description = "苹果支付平台配置")
public class PaymentAppleConfig {

    @Schema(description = "你 App 的 Bundle ID，用于限定请求作用域", example = "com.example.app")
    @JsonProperty("bundleId")
    private String bundleId;

    @Schema(description = "Apple 的 Issuer ID，标识你的 Apple Developer 帐号", example = "ABC123DEFG")
    @JsonProperty("issuerId")
    private String issuerId;

    @Schema(description = "App 在 App Store 上的唯一数字 ID", example = "123456789")
    @JsonProperty("appleAppId")
    private Long appleAppId;

    @Schema(description = "Apple 后台提供的 Key ID，与你的私钥成对使用", example = "XYZ987654")
    @JsonProperty("keyId")
    private String keyId;

    @Schema(description = "私钥路径（.p8 文件）", example = "/path/to/private_key.p8")
    @JsonProperty("privateKeyPath")
    private String privateKeyPath;
}
