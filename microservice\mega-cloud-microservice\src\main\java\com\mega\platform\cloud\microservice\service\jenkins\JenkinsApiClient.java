package com.mega.platform.cloud.microservice.service.jenkins;

import com.mega.platform.cloud.data.dto.jenkins.*;
import com.mega.platform.cloud.microservice.service.MicroserviceCommonService;
import com.offbytwo.jenkins.JenkinsServer;
import com.offbytwo.jenkins.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URI;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.Base64;
import java.util.Map;

@Slf4j
@Service
public class JenkinsApiClient {

    private String executeGroovyScript(JenkinsGroovyDTO dto, BaseJenkinsDTO baseDto) throws Exception {
        String auth = Base64.getEncoder().encodeToString((baseDto.getJenkinsUserName() + ":" + baseDto.getJenkinsUserToken()).getBytes());
        HttpClient client = HttpClient.newHttpClient();
        String url = String.format("%s/scriptText", baseDto.getJenkinsUrl());
        String body = "script=" + URLEncoder.encode(dto.getGroovyScriptStr(), "UTF-8");
        HttpRequest request = HttpRequest.newBuilder().uri(new URI(url)).header("Authorization", "Basic " + auth).header("Content-Type", "application/x-www-form-urlencoded").POST(HttpRequest.BodyPublishers.ofString(body)).build();
        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
        return response.body();
    }

    public String createJenkinsUserToken(CreateJenkinsUserTokenDTO dto, BaseJenkinsDTO baseDto) throws Exception {
        try {
            return executeGroovyScript(dto, baseDto);
        } catch (Exception e) {
            log.error("createJenkinsSshServer occur exception", e);
            throw e;
        }
    }

    public void createJenkinsUser(CreateJenkinsUserDTO dto, BaseJenkinsDTO baseDto) throws Exception {
        try {
            executeGroovyScript(dto, baseDto);
        } catch (Exception e) {
            log.error("createJenkinsSshServer occur exception", e);
            throw e;
        }
    }

    public void createJenkinsSshServer(CreateJenkinsSshServerDTO dto, BaseJenkinsDTO baseDto) throws Exception {
        try {
            executeGroovyScript(dto, baseDto);
        } catch (Exception e) {
            log.error("createJenkinsSshServer occur exception", e);
            throw e;
        }
    }

    public void createJenkinsView(String viewName, BaseJenkinsDTO baseDto) throws Exception {
        try {
            JenkinsServer jenkins = new JenkinsServer(new URI(baseDto.getJenkinsUrl()), baseDto.getJenkinsUserName(), baseDto.getJenkinsUserToken());
            jenkins.createView(viewName, "<hudson.model.ListView>\n</hudson.model.ListView>");
        } catch (Exception e) {
            log.error("getJenkinsView occur exception", e);
            throw e;
        }
    }

    public JobWithDetails getJenkinsJob(String jobName, BaseJenkinsDTO dto) throws Exception {
        try {
            JenkinsServer jenkins = new JenkinsServer(new URI(dto.getJenkinsUrl()), dto.getJenkinsUserName(), dto.getJenkinsUserToken());
            return jenkins.getJob(jobName);
        } catch (Exception e) {
            log.error("getJenkinsJob occur exception", e);
            throw e;
        }
    }

    public void getJenkinsJobXml(String jobName, BaseJenkinsDTO dto) throws Exception {
        try {
            JenkinsServer jenkins = new JenkinsServer(new URI(dto.getJenkinsUrl()), dto.getJenkinsUserName(), dto.getJenkinsUserToken());
            log.info(jenkins.getJobXml(jobName));
        } catch (Exception e) {
            log.error("getJenkinsJob occur exception", e);
            throw e;
        }
    }

    public void addJobToView(String jobName, String viewName, BaseJenkinsDTO baseDto) throws Exception {
        try {
            String auth = Base64.getEncoder().encodeToString((baseDto.getJenkinsUserName() + ":" + baseDto.getJenkinsUserToken()).getBytes());
            HttpClient client = HttpClient.newHttpClient();
            String url = String.format("%s/view/%s/addJobToView", baseDto.getJenkinsUrl(), viewName);
            // 参数是 name=jobName，注意 encode
            String body = "name=" + URLEncoder.encode(jobName, "UTF-8");
            HttpRequest request = HttpRequest.newBuilder().uri(new URI(url)).header("Authorization", "Basic " + auth).header("Content-Type", "application/x-www-form-urlencoded").POST(HttpRequest.BodyPublishers.ofString(body)).build();
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
        } catch (Exception e) {
            log.error("addJobToView occur exception", e);
            throw e;
        }
    }

    public void createJenkinsJob(CreateJenkinsJobDTO dto, BaseJenkinsDTO baseDto) throws Exception {
        try {
            JenkinsServer jenkins = new JenkinsServer(new URI(baseDto.getJenkinsUrl()), baseDto.getJenkinsUserName(), baseDto.getJenkinsUserToken());
            String xml = dto.getXml();
            for (Map.Entry<String, String> entry : dto.getParamMap().entrySet()) {
                xml = xml.replace(entry.getKey(), entry.getValue());
            }
            jenkins.createJob(dto.getJobName(), xml);
        } catch (Exception e) {
            log.error("createJenkinsJob occur exception", e);
            throw e;
        }
    }

    public Integer buildJenkinsJob(BuildJenkinsJobDTO dto, BaseJenkinsDTO baseDto) throws Exception {
        try {
            JenkinsServer jenkins = new JenkinsServer(new URI(baseDto.getJenkinsUrl()), baseDto.getJenkinsUserName(), baseDto.getJenkinsUserToken());
            JobWithDetails job = jenkins.getJob(dto.getJobName());
            log.info("buildJenkinsJob paramMap: {}", dto.getParamMap());
            QueueReference build = job.build(dto.getParamMap());
            QueueItem queueItem = jenkins.getQueueItem(build);
            while (queueItem.getExecutable() == null) {
                Thread.sleep(1000);
                queueItem = jenkins.getQueueItem(build);
                log.info("buildJenkinsJob task in queue. queueId:{}", queueItem.getId());
            }
            return queueItem.getExecutable().getNumber().intValue();
        } catch (Exception e) {
            log.error("buildJenkinsJob occur exception", e);
            throw e;
        }
    }

    public BuildWithDetails getJenkinsJobBuildStatus(String jobName, BaseJenkinsDTO dto, Integer buildId) throws Exception {
        try {
            JenkinsServer jenkins = new JenkinsServer(new URI(dto.getJenkinsUrl()), dto.getJenkinsUserName(), dto.getJenkinsUserToken());
            JobWithDetails job = jenkins.getJob(jobName);
            Build lastBuild = null;
            if (buildId == null) {
                lastBuild = job.getBuildByNumber(buildId);
            } else {
                lastBuild = job.getLastBuild();
            }
            BuildWithDetails details = lastBuild.details();
            return details;
        } catch (Exception e) {
            log.error("jenkinsJobBuildStatus occur exception", e);
            throw e;
        }
    }

    public static void main(String[] args) throws Exception {
        BaseJenkinsDTO baseDto = new BaseJenkinsDTO("http://120.27.232.208:9999", "tanzhenxing", "11312ac13f872f60b232ffbda373f5ebe5");
        CreateJenkinsUserDTO dto = new CreateJenkinsUserDTO("<EMAIL>");
        JenkinsApiClient client = new JenkinsApiClient();
        client.createJenkinsUser(dto, baseDto);
        System.out.println(client.createJenkinsUserToken(new CreateJenkinsUserTokenDTO(dto.getUserName()), baseDto));;
    }
}
