package com.mega.platform.cloud.data.vo.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "苹果交易验证响应")
public class PaymentAppleVerifyTransactionRespVO {

    @Schema(description = "苹果交易id")
    private String transactionId;

    @Schema(description = "苹果产品id")
    private String productId;

    @Schema(description = "校验结果")
    private boolean success;
}
