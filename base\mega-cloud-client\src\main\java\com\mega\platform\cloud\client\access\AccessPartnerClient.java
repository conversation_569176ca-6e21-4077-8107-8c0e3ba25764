package com.mega.platform.cloud.client.access;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.annotation.DescriptionTag;
import com.mega.platform.cloud.data.vo.access.AccessPartnerLicenseReqVO;
import com.mega.platform.cloud.data.vo.access.AccessPartnerTelemeringReqVO;
import com.mega.platform.cloud.data.vo.access.AccessPartnerVerifyReqVO;
import com.mega.platform.cloud.data.vo.access.AccessPartnerVerifyRespVO;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        value = "mega-cloud-access-" + "${spring.profiles.active}",
        contextId = "mega-cloud-access-partner-client",
        path = "/access/api")
@DescriptionTag(name = "合作方接口")
public interface AccessPartnerClient {

    @Operation(summary = "获取AES密钥")
    @PostMapping("/partner/verify")
    Result<AccessPartnerVerifyRespVO> verify(@Validated @RequestBody AccessPartnerVerifyReqVO vo);

    @Operation(summary = "License验证")
    @PostMapping("/partner/license")
    Result<?> license(@Validated @RequestBody AccessPartnerLicenseReqVO vo);

    @Operation(summary = "操作日志上报")
    @PostMapping("/partner/telemetering")
    Result<?> telemetering(@Validated @RequestBody AccessPartnerTelemeringReqVO vo);
}
