package com.mega.platform.cloud.data.vo.auth;

import com.mega.platform.cloud.data.vo.BaseReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "苹果token请求参数")
public class AuthVerifyAppleTokenReqVO extends BaseReqVO {

    @Schema(description = "苹果token", required = true)
    @NotBlank(message = "idToken不能为空")
    private String idToken;

    @Schema(description = "客户端IP", required = true)
    @NotBlank(message = "clientIp不能为空")
    private String clientIp;

    @Schema(description = "设备ID", required = true)
    @NotBlank(message = "deviceId不能为空")
    private String deviceId;
}
