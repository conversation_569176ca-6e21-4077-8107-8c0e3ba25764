package com.mega.platform.cloud.data.dto.monitor;

import com.mega.platform.cloud.data.entity.MonitorAlarm;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(name = "MetricsAlarmDTO", description = "监控告警指标 DTO")
public class MetricsAlarmDTO extends MonitorAlarm {

    @Schema(description = "告警间隔时间（秒）")
    private Integer alarmIntervalSecond;

    @Schema(description = "当前触发比例")
    private BigDecimal currentRatio;

    @Schema(description = "来源名称")
    private String sourceName;

    @Schema(description = "来源类型")
    private Integer sourceType;

    @Schema(description = "来源ID")
    private Long sourceId;
}
