logging:
  file:
    path: /opt/log/${spring.cloud.client.hostname}/
  logback:
    rollingpolicy:
      max-file-size: 128MB
spring:
  cloud:
    consul:
      enabled: true
      host: ***********
      port: 8500
      discovery:
        # 动态传入 ip-address
        ip-address:
        prefer-ip-address: true
        instance-id: ${spring.application.name}-${spring.cloud.consul.discovery.ip-address}-${server.port}
        service-name: ${spring.application.name}
        health-check-critical-timeout: 1m
