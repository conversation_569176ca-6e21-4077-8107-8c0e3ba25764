package com.mega.platform.cloud.data.dto.monitor;

import com.mega.platform.cloud.data.entity.Services;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "ServicesRunningStatusScanDTO", description = "服务运行状态扫描 DTO")
public class ServicesRunningStatusScanDTO extends Services {

    @Schema(description = "存活检查类型", example = "1")
    private Integer checkAliveType;

    @Schema(description = "服务 IP", example = "***********")
    private String serverIp;

    @Schema(description = "端口号", example = "8080")
    private Integer port;

    @Schema(description = "自定义脚本")
    private String customScript;

    // 报警信息
    @Schema(description = "服务分组名称", example = "支付服务组")
    private String servicesGroupName;

    @Schema(description = "项目名称", example = "商城项目")
    private String projectName;
}
