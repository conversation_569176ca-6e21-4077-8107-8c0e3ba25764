package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 权限管理-管理员路由更新绑定请求
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-管理员路由更新绑定请求")
public class AdminAccessUserRouterBindingUpdateReqVO {

    @Schema(description = "管理员ID", required = true)
    @NotNull(message = "管理员ID不能为空")
    private Long userId;

    @Schema(description = "路由ID", required = true)
    @NotNull(message = "路由ID不能为空")
    private Long routerId;

    @Schema(description = "删除标识: 0=未删除, 1=已删除", required = true)
    @NotNull(message = "删除标识不能为空")
    private Integer delsign;
}
