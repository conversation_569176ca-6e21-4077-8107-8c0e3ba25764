package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "应用创建请求参数")
public class AdminAppCreateReqVO {

    @Schema(description = "应用名称", example = "MyApp")
    private String name;

    @Schema(description = "描述", example = "这是一个示例应用")
    private String remark;
}
