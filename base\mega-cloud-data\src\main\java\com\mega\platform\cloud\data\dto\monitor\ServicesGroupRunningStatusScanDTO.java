package com.mega.platform.cloud.data.dto.monitor;

import com.mega.platform.cloud.data.entity.ServicesGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "ServicesGroupRunningStatusScanDTO", description = "服务分组运行状态扫描 DTO")
public class ServicesGroupRunningStatusScanDTO extends ServicesGroup {

    @Schema(description = "项目名称")
    private String projectName;

}
