package com.mega.platform.cloud.admin.dto;

import java.util.Date;
import java.util.Map;

import javax.persistence.Column;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AdminServicesDetailDTO {
    /** services基本信息 **/
    @Schema(description = "服务ID")
    private Long servicesId;

    @Schema(description = "服务名称")
    private String servicesName;

    @Schema(description = "服务备注")
    private String servicesRemark;

    @Schema(description = "服务状态")
    private Integer servicesStatus;

    @Schema(description = "服务运行状态")
    private Integer servicesRunningStatus;

    @Schema(description = "服务真实运行状态")
    private Integer servicesRealRunningStatus;


    /** servicesGroupId 基本信息 **/
    
    @Schema(description = "服务组ID")
    @Column(name = "id")
    private Long id; 

    @Schema(description = "服务组名")
    @Column(name = "name")
    private String name;

    @Schema(description = "产品ID")
    @Column(name = "project_id")
    private Long project_id;

    @Schema(description = "appId")
    @Column(name = "project_app_id")
    private Long project_app_id;

    @Schema(description = "微服务更新方式")
    @Column(name = "services_update_type")
    private Integer services_update_type;

    @Schema(description = "微服务环境")
    @Column(name = "services_env")
    private String services_env;

    @Schema(description = "微服务日志格式id")
    @Column(name = "services_log_format_id")
    private Long services_log_format_id;

    @Schema(description = "保活数量")
    @Column(name = "services_alive_num")
    private Integer services_alive_num;

    @Schema(description = "jenkins服务ID")
    @Column(name = "jenkins_services_id")
    private Long jenkins_services_id;

    @Schema(description = "jenkins模板ID")
    @Column(name = "jenkins_template_id")
    private Long jenkins_template_id;

    @Schema(description = "是否自研")
    @Column(name = "is_self")
    private Integer is_self;

    @Schema(description = "负责人")
    @Column(name = "admin_user_id")
    private Long admin_user_id;

    @Schema(description = "备注")
    @Column(name = "remark")
    private String remark;

    @Schema(description = "状态")
    @Column(name = "status")
    private Integer status;

    @Schema(description = "运行状态")
    @Column(name = "running_status")
    private Integer running_status;

    @Schema(description = "真实运行状态")
    @Column(name = "real_running_status")
    private Integer real_running_status;

    @Schema(description = "检查运行方式")
    @Column(name = "check_alive_type")
    private Integer check_alive_type;

    @Schema(description = "是否使用jenkins")
    @Column(name = "use_jenkins")
    private Byte use_jenkins;

    @Schema(description = "创建时间")
    @Column(name = "create_time")
    private Date create_time;

    @Schema(description = "更新时间")
    @Column(name = "update_time")
    private Date update_time;

    @Schema(description = "是否删除")
    @Column(name = "delsign")
    private Byte delsign;

    /** 最后一次jenkins_task_group信息 */ 
    @Schema(description = "最后一次任务组ID")
    private Long lastTaskGroupId;

    @Schema(description = "最后一次任务组操作类型")
    private Integer lastTaskGroupAction;

    @Schema(description = "最后一次任务组是否成功")
    private Integer lastTaskGroupIsSuccess;

    @Schema(description = "最后一次任务组完成时间")
    private Date lastTaskGroupCompleteTime;

    /** 最后一次jenkins_task信息 */
    @Schema(description = "最后一次任务ID")
    private Long lastTaskId;

    @Schema(description = "最后一次任务操作类型")
    private Integer lastTaskAction;

    @Schema(description = "最后一次任务是否成功")
    private Integer lastTaskIsSuccess;

    @Schema(description = "最后一次任务完成时间")
    private Date lastTaskCompleteTime;

    @Schema(description = "最后一次任务Jenkins链接")
    private String lastTaskJenkinsJobUrl;


}
