package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@Schema(description = "服务上线下线请求参数")
public class AdminServicesStatusEditReqVO {

    @Schema(description = "更新服务id", required = true)
    @NotNull(message = "更新服务id不能为空")
    private Long servicesId;

    @Schema(description = "上线-1 下线-0", required = true)
    @NotNull(message = "状态不能为空")
    private Integer status;
}
