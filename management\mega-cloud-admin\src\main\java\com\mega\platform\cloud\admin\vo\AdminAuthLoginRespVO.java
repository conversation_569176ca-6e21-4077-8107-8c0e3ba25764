package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 管理员登录响应数据
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "管理员登录响应数据")
public class AdminAuthLoginRespVO {

    @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

}
