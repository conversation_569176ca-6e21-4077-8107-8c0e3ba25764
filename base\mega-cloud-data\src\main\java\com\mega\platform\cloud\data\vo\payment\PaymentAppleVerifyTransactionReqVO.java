package com.mega.platform.cloud.data.vo.payment;

import com.mega.platform.cloud.data.vo.BaseReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

@Data
@Accessors(chain = true)
@Schema(description = "苹果交易验证请求参数")
public class PaymentAppleVerifyTransactionReqVO extends PaymentBaseReqVO {

    @Schema(description = "苹果交易id", required = true)
    @NotBlank(message = "苹果交易id不能为空")
    private String transactionId;
}
