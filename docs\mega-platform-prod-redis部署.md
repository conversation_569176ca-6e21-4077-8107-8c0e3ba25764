## 1 挂载磁盘

```shell

# 服务器改名
sudo hostnamectl set-hostname 新的主机名
# 查看当前主机名
hostnamectl status
```

vdb,参考[[linux挂载磁盘]]

```shell
sudo mkdir /mnt/data

sudo mkfs.ext4 /dev/vdb

sudo mount /dev/vdb /mnt/data

echo '/dev/vdb /mnt/data ext4 defaults 0 2' >> /etc/fstab

sudo mount -a
```

## 2安装docker

### 2.1 ubunut

```shell
# Add Docker's official GPG key:
sudo apt-get update
sudo apt-get install ca-certificates curl
sudo install -m 0755 -d /etc/apt/keyrings
sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
sudo chmod a+r /etc/apt/keyrings/docker.asc

# Add the repository to Apt sources:
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
  $(. /etc/os-release && echo "${UBUNTU_CODENAME:-$VERSION_CODENAME}") stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt-get update

sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

sudo usermod -aG docker $USER
```

### 2.2 debain

```shell
# Add Docker's official GPG key:
sudo apt-get update
sudo apt-get install ca-certificates curl
sudo install -m 0755 -d /etc/apt/keyrings
sudo curl -fsSL https://download.docker.com/linux/debian/gpg -o /etc/apt/keyrings/docker.asc
sudo chmod a+r /etc/apt/keyrings/docker.asc

# Add the repository to Apt sources:
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/debian \
  $(. /etc/os-release && echo "$VERSION_CODENAME") stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt-get update

sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
```

### 新建 /etc/docker/daemon.json

```shell
{
    "live-restore": true,
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "100m",
        "max-file": "3"
    },
    "storage-driver": "overlay2",
    "data-root": "/mnt/data/docker",
    "exec-opts": [
        "native.cgroupdriver=systemd"
    ],
    "insecure-registries": [
        "gitlab.53site.com:1180",
        "172.25.220.218:1180"
    ],
    "registry-mirrors": [
        "https://wa331s09.mirror.aliyuncs.com",
        "https://dockerproxy.com",
        "https://docker.m.daocloud.io",
        "https://cr.console.aliyun.com",
        "https://ccr.ccs.tencentyun.com",
        "https://hub-mirror.c.163.com",
        "https://mirror.baidubce.com",
        "https://docker.nju.edu.cn",
        "https://docker.mirrors.sjtug.sjtu.edu.cn",
        "https://github.com/ustclug/mirrorrequest",
        "https://registry.docker-cn.com"
    ]
}
```

```shell
sudo systemctl daemon-reload
sudo systemctl restart docker
```

## 3 安装redis

```shell
mkdir /mnt/data/redis
mkdir /mnt/data/redis/logs 
chmod 777 /mnt/data/redis/logs
mkdir /mnt/data/redis/save
chmod 777 /mnt/data/redis/save

mkdir /mnt/data/sentinel
mkdir /mnt/data/sentinel/logs
chmod 777 /mnt/data/sentinel/logs
mkdir /mnt/data/sentinel/save
chmod 777 /mnt/data/sentinel/save

```

### redis配置

生成redis master 配置

```shell
touch /mnt/data/redis/redis.conf
sudo printf 'port 5537
bind 0.0.0.0
protected-mode no
requirepass LA1954b!
masterauth LA1954b
appendonly yes
maxmemory 3gb
logfile /var/log/redis/redis.log' > /mnt/data/redis/redis.conf

```

生成redis slave 配置

```shell
touch /mnt/data/redis/redis.conf
sudo printf 'port 5537
bind 0.0.0.0
requirepass LA1954b!
masterauth LA1954b!
replicaof *********** 5537
appendonly yes
maxmemory 3gb
logfile /var/log/redis/redis.log' > /mnt/data/redis/redis.conf


```

生成sentinel配置

```shell
touch /mnt/data/sentinel/sentinel.conf
sudo printf 'port 25537
bind 0.0.0.0
protected-mode no
sentinel monitor mymaster *********** 5537 2
sentinel auth-pass mymaster LA1954b!
sentinel down-after-milliseconds mymaster 5000
sentinel failover-timeout mymaster 10000
sentinel parallel-syncs mymaster 1
logfile /var/log/redis/redis.log' > /mnt/data/sentinel/sentinel.conf
```

### redis启动

redis master start

```shell
docker run -d --name redis --restart=always \
-p 5537:5537 \
-v /mnt/data/redis/redis.conf:/usr/local/etc/redis/redis.conf \
-v /mnt/data/redis/save:/data \
-v /mnt/data/redis/logs:/var/log/redis \
redis redis-server /usr/local/etc/redis/redis.conf --appendonly yes 
```

sentinel start

```shell
docker run -d --name sentinel --network host --restart=always \
-v /mnt/data/sentinel/sentinel.conf:/usr/local/etc/redis/redis.conf \
-v /mnt/data/sentinel/save:/data \
-v /mnt/data/sentinel/logs:/var/log/redis \
redis redis-sentinel /usr/local/etc/redis/redis.conf
```

## 测试

redis-cli -h *********** -p 25537 sentinel get-master-addr-by-name mymaster
