package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@Schema(description = "应用响应数据")
public class AdminAppRespVO {

    @Schema(description = "应用ID")
    private Long id;

    @Schema(description = "应用Key")
    private String appKey;

    @Schema(description = "应用密钥")
    private String appSecret;

    @Schema(description = "应用名称")
    private String name;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "状态：0不可用，1正常，2挂起，3审核中")
    private Integer status;

    @Schema(description = "描述")
    private String remark;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
}
