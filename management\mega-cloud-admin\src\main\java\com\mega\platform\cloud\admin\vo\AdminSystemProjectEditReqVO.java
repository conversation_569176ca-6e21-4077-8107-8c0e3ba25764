package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 系统项目编辑 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AdminSystemProjectEditReqVO extends AdminProjectEditReqVO {

    @Schema(description = "项目ID", required = true)
    @NotNull(message = "项目ID不能为空")
    private Long projectId;
}
