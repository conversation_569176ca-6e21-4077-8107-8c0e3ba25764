package com.mega.platform.cloud.admin.vo;

import javax.validation.constraints.NotNull;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(name = "AdminAppEditReqVO", description = "应用编辑请求参数")
public class AdminAppEditReqVO {

    @Schema(description = "应用ID")
    @NotNull(message = "应用ID不能为空")
    private Long id;

    @Schema(description = "应用名称")
    private String name;

    @Schema(description = "状态：0不可用，1正常，2挂起，3审核中")
    private Integer status;

    @Schema(description = "描述")
    private String remark;
}
