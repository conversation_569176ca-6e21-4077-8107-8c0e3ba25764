package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 权限管理-路由列表响应参数
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-路由列表响应参数")
public class AdminAccessRouterListRespVO {

    @Schema(description = "路由ID", example = "1")
    private Long id;

    @Schema(description = "后端路由路径", example = "/admin/api/system/user/list")
    private String backendPath;

    @Schema(description = "前端路由路径", example = "/dashboard")
    private String frontendPath;

    @Schema(description = "前端路由名称", example = "Dashboard")
    private String frontendName;

    @Schema(description = "路由描述", example = "仪表板")
    private String description;

    @Schema(description = "父路由ID", example = "0")
    private Long parentAdminRouterId;

    @Schema(description = "创建时间", example = "2024-01-01 12:00:00")
    private Date createTime;

    @Schema(description = "更新时间", example = "2024-01-01 12:00:00")
    private Date updateTime;

    @Schema(description = "删除标识", example = "0")
    private Integer delsign;
}
