package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@Schema(description = "jenkins模板参数列表请求数据")
public class AdminBaseJenkinsTemplateParamListReqVO {

    @NotNull(message = "模板ID不能为空")
    @Schema(description = "模板ID，关联jenkins_job_template(id)", required = true)
    private Long jenkinsTemplateId;

    @NotNull(message = "参数类型不能为空")
    @Schema(description = "参数类型 1-group上的 2-service上的", required = true)
    private Integer servicesDataType;
}
