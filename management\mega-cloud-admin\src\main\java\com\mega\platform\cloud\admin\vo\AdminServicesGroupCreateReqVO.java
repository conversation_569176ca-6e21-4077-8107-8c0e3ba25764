package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigInteger;
import java.util.List;

/**
 * 服务组创建请求参数
 */
@Data
@Accessors(chain = true)
@Schema(name = "服务组创建请求参数", description = "服务组创建请求参数")
public class AdminServicesGroupCreateReqVO {

    /**
     * 模板参数值（JSON字符串）
     */
    @Schema(
            description = "模板参数值，JSON格式字符串。示例: {\"appName\":\"test\",\"cloudRestartFileName\":\"测试\",\"gitBranch\":\"master\",\"gitUrl\":\"test\",\"groupId\":\"testGroupId\",\"moduleName\":\"testModuleName\"}",
            example = "{\"appName\":\"test\",\"cloudRestartFileName\":\"测试\",\"gitBranch\":\"master\",\"gitUrl\":\"test\",\"groupId\":\"testGroupId\",\"moduleName\":\"testModuleName\"}"
    )
    private String jenkinsParams;

    @Schema(description = "管理员用户ID（内部使用）", hidden = true)
    private Long adminUserId;

    /**
     * 模板id
     */
    @Schema(description = "Jenkins模板ID", example = "1")
    private Long jenkinsTemplateId;

    /**
     * jenkins实例id
     */
    @NotNull(message = "jenkins实例id不能为空")
    @Schema(description = "Jenkins服务实例ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long jenkinsServiceId;

    @NotNull(message = "appId不能为空")
    @Schema(description = "关联的应用ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long projectAppId;

    /**
     * 服务组名
     */
    @NotBlank(message = "服务组名不能为空")
    @Schema(description = "服务组名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "test-service-group")
    private String servicesGroupName;

    /**
     * 启动方式
     */
    @NotNull(message = "启动方式不能为空")
    @Schema(
            description = "服务启动方式: 1-普通重启, 2-滚服重启, 3-导流重启, 4-脚本运行",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "1"
    )
    private Integer serviceUpdateId;

    /**
     * 环境
     */
    @NotBlank(message = "环境不能为空")
    @Schema(
            description = "部署环境标识: dev(开发), test(测试), beta(预发布), prod(生产)",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "dev"
    )
    private String serviceEnv;

    /**
     * 保活数量
     */
    @Schema(
            description = "滚服/导流重启时需要保活的服务数量（默认0）",
            example = "2"
    )
    private Integer serviceAliveNum = 0;

    /**
     * 保活检测方式
     */
    @Schema(
            description = "保活状态检测方式（默认1）",
            example = "1"
    )
    private Integer checkAliveType = 1;

    /**
     * 微服务日志格式id
     */
    @Schema(description = "微服务日志格式ID（扩展字段）")
    private Long servicesLogFormatId;

    /**
     * 服务类型
     */
    @Schema(
            description = "服务类型: 1-自研服务, 3-第三方服务（扩展字段）",
            example = "1"
    )
    private Integer isSelf;

    /**
     * 备注
     */
    @Schema(description = "服务组备注信息（扩展字段）", example = "用户服务组")
    private String remark;

    /**
     * 组标签
     */
    @Schema(description = "服务组标签列表（扩展字段）", example = "[1,2,3]")
    private List<BigInteger> tags;

    /**
     * Jenkins使用标识
     */
    @Schema(
            description = "是否使用Jenkins: 0-不使用, 1-使用（默认1）",
            example = "1"
    )
    private Integer useJenkins = 1;
}