package com.mega.platform.cloud.auth.controller;

import com.mega.platform.cloud.common.constant.JwtConstants;
import com.mega.platform.cloud.common.utils.JwtTokenUtil;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.vo.BaseReqVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Tag(name = "auth测试接口", description = "认证模块测试相关接口")
@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {
    private final JwtTokenUtil jwtTokenUtil;

    public TestController(JwtTokenUtil jwtTokenUtil) {
        this.jwtTokenUtil = jwtTokenUtil;
    }

    @Operation(summary = "测试token生成")
    @PostMapping("/public/generate/token")
    public Result<?> generateToken() {
        BaseReqVO baseReqVO = new BaseReqVO();
        baseReqVO.setAppId(1L);
        return Results.success(jwtTokenUtil.generateToken(baseReqVO));
    }

    @Operation(summary = "测试token解析")
    @PostMapping("/parse/token")
    public Result<?> parseToken(HttpServletRequest request) {
        log.info(String.valueOf(request.getAttribute(JwtConstants.CLAIM_APP_ID)));
        return Results.success();
    }
}
