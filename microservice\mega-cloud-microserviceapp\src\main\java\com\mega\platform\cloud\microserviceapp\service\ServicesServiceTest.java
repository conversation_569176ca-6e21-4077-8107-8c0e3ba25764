package com.mega.platform.cloud.microserviceapp.service;

import com.mega.platform.cloud.common.service.CommonFeishuService;
import com.mega.platform.cloud.common.service.CommonMailService;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesGroupReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesReqVO;
import com.mega.platform.cloud.microservice.service.JenkinsService;
import com.mega.platform.cloud.microservice.service.MicroserviceCommonService;
import com.mega.platform.cloud.microservice.service.ServicesService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

@SpringBootTest
public class ServicesServiceTest {

    private final ServicesService servicesService;

    @Autowired
    public ServicesServiceTest(ServicesService servicesService) {
        this.servicesService = servicesService;
    }

    @Test
    public void testCreateServiceGroup() throws Exception {
        CreateServicesGroupReqVO createServicesGroupReqVO = new CreateServicesGroupReqVO();
        Map<String, String> map = new HashMap<>();
        // ------------------------------
        String moduleName = "rank";
        String gameRegionId = "0";
        String env = "test";
        map.put("moduleName", moduleName);
        map.put("gitUrl", "http://gitlab.53site.com/games/mining/mining-cloud.git");
        map.put("gitBranch", env);
        map.put("cloudRestartFileName", "mining-cloud.sh");
        map.put("appName", "mining-cloud");
        map.put("groupId", "com.mega.mining.cloud");
        map.put("gameRegionId", gameRegionId);
        map.put("mysqlGameMainMaster", "172.25.220.241:3306");
        map.put("mysqlGameMainSlave", "172.25.220.241:3306");
        map.put("mysqlGameRegionMaster", "172.25.220.241:3306");
        map.put("mysqlGameRegionSlave", "172.25.220.241:3306");
        map.put("redisNodes", "172.25.221.71:26379;172.25.221.71:26380;172.25.221.71:26381");
        createServicesGroupReqVO.setServicesGroupName("mining-cloud-" + moduleName + "-" + gameRegionId);
        createServicesGroupReqVO.setJenkinsTemplateId(3L);
        createServicesGroupReqVO.setServiceEnv(env);
        // ------------------------------
        // ------------------------------
        //        String moduleName = "account";
        //        map.put("moduleName", moduleName);
        //        map.put("gitUrl", "http://gitlab.53site.com/mega-game/game-services/gossipharbor-cloud.git");
        //        map.put("gitBranch", "master");
        //        map.put("cloudRestartFileName", "gossipharbor-cloud.sh");
        //        map.put("appName", "gossipharbor-cloud");
        //        map.put("groupId", "com.mega.gossipharbor.cloud");
        //        map.put("gameRegionId", "1001");
        //        createServicesGroupReqVO.setServicesGroupName("gossipharbor-cloud-" + moduleName);
        //        createServicesGroupReqVO.setJenkinsTemplateId(2L);
        //        createServicesGroupReqVO.setServiceEnv("test");
        // ------------------------------
        // ------------------------------
//                String moduleName = "microserviceapp";
//                String env = "prod";
//                map.put("moduleName", moduleName);
//                map.put("gitUrl", "http://gitlab.53site.com/WerewolfServer/Services/mega-cloud.git");
//                map.put("gitBranch", env);
//                map.put("cloudRestartFileName", "mega-cloud.sh");
//                map.put("appName", "mega-cloud");
//                map.put("groupId", "com.mega.platform.cloud");
//                createServicesGroupReqVO.setServicesGroupName("platform-" + moduleName + "-" + env);
//                createServicesGroupReqVO.setJenkinsTemplateId(1L);
//                createServicesGroupReqVO.setServiceEnv(env);
        // ------------------------------
        createServicesGroupReqVO.setJenkinsParams(map);
        createServicesGroupReqVO.setAdminUserId(1L);
        createServicesGroupReqVO.setJenkinsServiceId(1L);
        createServicesGroupReqVO.setProjectId(1L);
        createServicesGroupReqVO.setProjectAppId(1L);
        //        createServicesGroupReqVO.setServiceEnv("test");
        createServicesGroupReqVO.setServiceUpdateId(1);
        createServicesGroupReqVO.setServiceAliveNum(1);
        createServicesGroupReqVO.setCheckAliveType(1);
        servicesService.createServicesGroup(createServicesGroupReqVO);
        System.out.println("testCreateServiceGroup");
    }

    @Test
    public void testCreateService() throws Exception {
        CreateServicesReqVO createServicesReqVO = new CreateServicesReqVO();
        Map<String, String> map = new HashMap<>();
        // ------------------------------
                String moduleName = "rank";
                map.put("port", "8943");
                createServicesReqVO.setServiceGroupId(14L);
                createServicesReqVO.setServicesName("mining-" + moduleName);
                createServicesReqVO.setTargetEcsServerId(1L);
        // ------------------------------
        // ------------------------------
//        String gameRegionId = "0";
//        String moduleName = "account";
//        map.put("port", "8940");
//        createServicesReqVO.setServiceGroupId(10L);
//        createServicesReqVO.setServicesName("mining-cloud-" + moduleName + "-" + gameRegionId);
//        createServicesReqVO.setTargetEcsServerId(1L);
        // ------------------------------
        map.put("mem", "256m");
        createServicesReqVO.setJenkinsParams(map);
        createServicesReqVO.setAdminUserId(1L);
        servicesService.createServices(createServicesReqVO);
        System.out.println("testCreateService");
    }

    @Test
    public void test() throws Exception {
        Thread.sleep(10000);
    }
}
