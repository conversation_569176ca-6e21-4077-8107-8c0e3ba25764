package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "支付订单分页查询请求")
public class AdminPaymentOrderListReqVO {

    @Schema(description = "页码，从1开始", example = "1")
    private Integer pageNum;

    @Schema(description = "每页大小，最大100", example = "20")
    private Integer pageSize;

    @Schema(description = "订单号", example = "20250722XXX")
    private Long orderId;

    @Schema(description = "父订单ID，查询其及其子订单", example = "10001")
    private Long parentId;

    @Schema(description = "平台代码，例如 ALIPAY、WECHAT、APPLE", example = "ALIPAY")
    private String platformCode;

    @Schema(description = "订单类型，例如 1订阅 0 内购", example = "0")
    private Boolean isSubscription;

    @Schema(description = "订单状态（0=初始化, 1=成功, 2=失败, 3=已退款）", example = "1")
    private Integer status;

    @Schema(description = "支付时间-起始", example = "2025-07-01T00:00:00")
    private LocalDateTime payTimeStart;

    @Schema(description = "支付时间-结束", example = "2025-07-23T23:59:59")
    private LocalDateTime payTimeEnd;
}
