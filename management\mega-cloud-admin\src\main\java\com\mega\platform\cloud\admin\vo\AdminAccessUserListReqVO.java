package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 权限管理-管理员列表查询请求
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-管理员列表查询请求")
public class AdminAccessUserListReqVO {

    // @Schema(description = "页码", example = "1")
    // private Integer pageNum = 1;

    // @Schema(description = "每页大小", example = "20")
    // private Integer pageSize = 20;

    @Schema(description = "用户名")
    private String adminUsername;

    @Schema(description = "删除标识: 0=未删除, 1=已删除")
    private Integer delsign;
}
