package com.mega.platform.cloud.client.microservice;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.annotation.DescriptionTag;
import com.mega.platform.cloud.data.vo.microservice.BuildServicesGroupReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesGroupReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesGroupRespVO;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        value = "mega-cloud-microservice-" + "${spring.profiles.active}",
        contextId = "mega-cloud-microservice-servicesGroup-client",
        path = "/microservice/api")
@DescriptionTag(name = "服务组接口")
public interface ServicesGroupClient {

    @Operation(summary = "创建服务组")
    @PostMapping("/servicesGroup/create")
    Result<CreateServicesGroupRespVO> createServicesGroup(@Validated @RequestBody CreateServicesGroupReqVO vo) throws Exception;

    @Operation(summary = "构建服务组")
    @PostMapping("/servicesGroup/build")
    Result<?> buildServicesGroup(@Validated @RequestBody BuildServicesGroupReqVO vo) throws Exception;
}
