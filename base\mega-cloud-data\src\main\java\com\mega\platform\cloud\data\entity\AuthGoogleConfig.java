package com.mega.platform.cloud.data.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "AuthGoogleConfig", description = "Google 平台配置")
public class AuthGoogleConfig {

    @Schema(description = "Google 客户端 ID", example = "your-google-client-id")
    @JsonProperty("clientId")
    private String clientId;

    @Schema(description = "Google iOS 客户端 ID", example = "your-ios-client-id")
    @JsonProperty("iosClientId")
    private String iosClientId;

    @Schema(description = "Google Android 客户端 ID", example = "your-android-client-id")
    @JsonProperty("androidClientId")
    private String androidClientId;
}
