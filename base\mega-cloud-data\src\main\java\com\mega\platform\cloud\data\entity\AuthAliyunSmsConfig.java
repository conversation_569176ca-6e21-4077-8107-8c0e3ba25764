package com.mega.platform.cloud.data.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "AuthAliyunSmsConfig", description = "阿里云短信配置")
public class AuthAliyunSmsConfig {

    @Schema(description = "阿里云 AccessKey ID", example = "LTAI4xxxxxxxxxxxxxx")
    @JsonProperty("accessKeyId")
    private String accessKeyId;

    @Schema(description = "阿里云 AccessKey Secret", example = "sDF234xxxxxxxxxxxxxx")
    @JsonProperty("accessKeySecret")
    private String accessKeySecret;

    @Schema(description = "阿里云 Region，例如 cn-hangzhou", example = "cn-hangzhou")
    @JsonProperty("region")
    private String region;

    @Schema(description = "短信服务 Endpoint，例如 dysmsapi.aliyuncs.com", example = "dysmsapi.aliyuncs.com")
    @JsonProperty("endpoint")
    private String endpoint;

    @Schema(description = "验证码有效时间（秒）", example = "600")
    @JsonProperty("codeValidSecond")
    private Integer codeValidSecond;

    @Schema(description = "短信签名名称", example = "你的签名名称")
    @JsonProperty("signName")
    private String signName;
}
