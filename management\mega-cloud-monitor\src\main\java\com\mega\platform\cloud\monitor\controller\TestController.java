package com.mega.platform.cloud.monitor.controller;

import com.mega.platform.cloud.client.monitor.MonitorMetricsClient;
import com.mega.platform.cloud.common.utils.RequestUtils;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.dto.monitor.MetricsCollectDTO;
import com.mega.platform.cloud.data.vo.monitor.MetricsEcsCollectReqVO;
import com.mega.platform.cloud.data.vo.monitor.MetricsServicesCollectReqVO;
import com.mega.platform.cloud.monitor.service.metrics.MetricsCollectService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import static com.mega.platform.cloud.common.constant.MonitorConstants.METRICS_SOURCE_TYPE_ECS;
import static com.mega.platform.cloud.common.constant.MonitorConstants.METRICS_SOURCE_TYPE_SERVICES;

@Tag(name = "指标接口", description = "指标相关接口")
@Slf4j
@RestController
@RequiredArgsConstructor
public class TestController implements MonitorMetricsClient {

    private final MetricsCollectService metricsCollectService;

    @Operation(summary = "服务器指标上报", description = "上报 ECS 指标")
    @Override
    @PostMapping("/metrics/ecs/collect")
    public Result<?> metricsEcsCollect(@Validated @RequestBody MetricsEcsCollectReqVO reqVO) throws Exception {
        MetricsCollectDTO dto = new MetricsCollectDTO();
        BeanUtils.copyProperties(reqVO, dto);
        //TODO TZX dto.setClientIp(RequestUtils.getClientIp(request));
//        dto.setClientIp(RequestUtils.getClientIp(request));
        dto.setSourceType(METRICS_SOURCE_TYPE_ECS);
        dto.setCollectTime(reqVO.getCollectTime());
        metricsCollectService.metricsCollect(dto);
        return Results.success();
    }

    @Operation(summary = "业务指标上报", description = "上报业务服务指标")
    @Override
    @PostMapping("/metrics/services/collect")
    public Result<?> metricsServicesCollect(@Validated @RequestBody MetricsServicesCollectReqVO reqVO) throws Exception {
        MetricsCollectDTO dto = new MetricsCollectDTO();
        BeanUtils.copyProperties(reqVO, dto);
        dto.setSourceType(METRICS_SOURCE_TYPE_SERVICES);
        dto.setCollectTime(reqVO.getCollectTime());
        metricsCollectService.metricsCollect(dto);
        return Results.success();
    }
}
