package com.mega.platform.cloud.data.vo.access;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

@Data
@Accessors(chain = true)
@Schema(name = "AccessPartnerTelemeringReqVO", description = "合作方操作日志上报请求参数")
public class AccessPartnerTelemeringReqVO {

    @Schema(description = "appKey", example = "app_1752202145", required = true)
    @NotBlank(message = "appKey不能为空")
    private String appKey;

    @Schema(description = "服务名称", example = "gossipharbor-cloud-exchange", required = true)
    @NotBlank(message = "服务名称不能为空")
    private String serverName;

    @Schema(description = "操作日志数据", example = "{\"action\":\"login\",\"user\":\"admin\"}", required = true)
    @NotBlank(message = "日志数据不能为空")
    private String logData;
}
