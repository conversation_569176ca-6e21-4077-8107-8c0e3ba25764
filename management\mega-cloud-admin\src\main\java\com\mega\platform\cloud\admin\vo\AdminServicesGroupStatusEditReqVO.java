package com.mega.platform.cloud.admin.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 服务组状态编辑请求参数
 */
@Data
@Accessors(chain = true)
@Schema(name = "服务组状态编辑请求参数", description = "服务组状态编辑请求参数")
public class AdminServicesGroupStatusEditReqVO {

    /**
     * 服务组ID
     */
    @NotNull(message = "服务组ID不能为空")
    @Schema(description = "服务组ID", required = true, example = "1")
    private Long servicesGroupId;

    /**
     * 状态 0：下线 1：上线
     */
    @NotNull(message = "状态不能为空")
    @Schema(description = "状态，0：下线 1：上线", required = true, example = "1")
    private Integer status;
}
