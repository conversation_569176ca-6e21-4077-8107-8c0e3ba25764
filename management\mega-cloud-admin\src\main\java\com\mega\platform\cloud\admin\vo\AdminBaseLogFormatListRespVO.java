package com.mega.platform.cloud.admin.vo;

import com.mega.platform.cloud.data.entity.ServicesLogFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@Schema(description = "日志格式列表响应数据")
public class AdminBaseLogFormatListRespVO {

    @Schema(description = "日志格式列表")
    private List<ServicesLogFormat> servicesLogFormats;
}
