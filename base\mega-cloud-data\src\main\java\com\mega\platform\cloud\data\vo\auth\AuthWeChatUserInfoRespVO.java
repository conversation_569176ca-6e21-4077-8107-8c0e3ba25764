package com.mega.platform.cloud.data.vo.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "微信用户信息返回参数")
public class AuthWeChatUserInfoRespVO {

    @Schema(description = "openid")
    private String openId;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "性别，1男，2女，0未知")
    private Integer sex;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "国家")
    private String country;

    @Schema(description = "头像地址")
    private String headImgUrl;

    @Schema(description = "unionId")
    private String unionId;

    @Schema(description = "验证结果")
    private boolean success;
}
