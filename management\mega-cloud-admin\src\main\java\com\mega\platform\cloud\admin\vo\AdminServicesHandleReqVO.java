package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@Schema(name = "停止服务请求参数", description = "停止服务请求参数")
public class AdminServicesHandleReqVO {

    @Schema(description = "更新服务id", required = true)
    @NotNull
    private Long servicesId;
}
