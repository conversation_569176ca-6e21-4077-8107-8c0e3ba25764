package com.mega.platform.cloud.admin.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 服务组操作请求参数
 */
@Data
@Accessors(chain = true)
@Schema(name = "服务组操作请求参数", description = "服务组操作请求参数")
public class AdminServicesGroupActionReqVO {

    /**
     * 服务组ID
     */
    @NotNull(message = "服务组ID不能为空")
    @Schema(description = "服务组ID", required = true, example = "1")
    private Long servicesGroupId;

    /**
     * 操作类型
     */
    @NotNull(message = "操作类型不能为空")
    @Schema(
            description = "操作类型，取值范围：NOTHING(0, \"nothing\", \"\"), RESTART(1, \"restart\", \"重启\"), STOP(2, \"stop\", \"停止\")",
            required = true,
            example = "1",
            allowableValues = {"0", "1", "2"}
    )
    private Integer action;

    @Schema(hidden = true)
    private Long adminUserId;
}
