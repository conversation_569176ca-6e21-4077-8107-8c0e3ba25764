package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 权限管理-角色项目绑定更新请求参数
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-角色项目绑定更新请求参数")
public class AdminAccessRoleProjectBindingUpdateReqVO {

    @Schema(description = "角色ID", example = "1", required = true)
    @NotNull(message = "角色ID不能为空")
    private Long adminRoleId;

    @Schema(description = "项目ID", example = "1", required = true)
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    @Schema(description = "是否删除", example = "0", required = true)
    @NotNull(message = "是否删除不能为空")
    private Integer delsign;

}
