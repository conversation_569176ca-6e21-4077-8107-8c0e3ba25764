package com.mega.platform.cloud.data.vo.microservice;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "构建服务组请求参数")
public class BuildServicesGroupReqVO {

    @Schema(description = "服务组ID")
    private Long servicesGroupId;

    @Schema(description = "操作类型")
    private Integer action;

    @Schema(description = "管理员用户ID")
    private Long adminUserId;
}
