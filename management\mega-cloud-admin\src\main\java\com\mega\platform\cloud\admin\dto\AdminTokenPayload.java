package com.mega.platform.cloud.admin.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Set;

import io.swagger.v3.oas.annotations.media.Schema;

@Data
@Accessors(chain = true)
public class AdminTokenPayload {

     @Schema(description = "所有项目ID列表")
     private Set<Long> projectIds;

     @Schema(description = "所有后端列表")
     private Set<String> backendPaths;

     @Schema(description = "所有角色id列表")
     private Set<Long> roleIds;

     @Schema(description = "token版本号")
     private Long tokenVersion;

     @Schema(description = "用户id")
     private Long adminUserId;
}
