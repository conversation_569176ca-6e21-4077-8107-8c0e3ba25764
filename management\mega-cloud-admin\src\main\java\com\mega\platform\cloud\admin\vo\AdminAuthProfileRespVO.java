package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 管理员用户详细信息响应数据
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "管理员用户详细信息响应数据")
public class AdminAuthProfileRespVO {

    @Schema(description = "管理员用户ID", example = "1")
    private Long adminUserId;

    @Schema(description = "管理员用户名", example = "admin")
    private String adminUserName;

    @Schema(description = "管理员角色列表")
    private List<AdminRoleVO> roles;

    @Schema(description = "管理员拥有的项目列表")
    private List<AdminProjectVO> projects;

    @Schema(description = "管理员有用的路由列表")
    private List<AdminFirstRouterVO> routers;


    @Data
    @Accessors(chain = true)
    @Schema(description = "管理员角色数据")
    public static class AdminRoleVO {
        @Schema(description = "角色ID", example = "1")
        private Long id; // 角色ID

        @Schema(description = "角色名称", example = "管理员")
        private String name; // 角色名称

        @Schema(description = "角色描述", example = "管理员角色")
        private String description; // 角色描述
    }


    @Data
    @Accessors(chain = true)
    @Schema(description = "管理员一级路由数据")
    public static class AdminFirstRouterVO {
        private Long id;

        @Schema(description = "前端路由路径", example = "/access")
        private String frontendPath; // 前端路由路径

        @Schema(description = "前端路由名称", example = "权限管理")
        private String frontendName; // 前端路由名称

        @Schema(description = "子路由列表")
        private List<AdminSecondRouterVO> childRouters;

        // @Schema(description = "路由描述", example = "系统仪表盘")
        // private String description; // 路由描述
    }

    @Data
    @Accessors(chain = true)
    @Schema(description = "管理员子路由数据")
    public static class AdminSecondRouterVO {
        private Long id; // routerID

        @Schema(description = "前端路由路径", example = "/access/user")
        private String frontendPath; // 前端路由路径

        @Schema(description = "前端路由名称", example = "管理员管理")
        private String frontendName; // 前端路由名称

        @Schema(description = "子路由列表")
        private List<AdminThirdRouterVO> childRouters;
    }

    @Data
    @Accessors(chain = true)
    @Schema(description = "管理员子路由数据")
    public static class AdminThirdRouterVO {
        private Long id; // routerID

        @Schema(description = "前端路由路径", example = "/dashboard")
        private String frontendPath; // 前端路由路径

        @Schema(description = "前端路由名称", example = "系统仪表盘")
        private String frontendName; // 前端路由名称
    }


    @Data
    @Accessors(chain = true)
    @Schema(description = "管理员项目数据")
    public static class AdminProjectVO {
        @Schema(description = "项目id", example = "1")
        private Long id; // 项目ID

        @Schema(description = "项目名称", example = "项目名称")
        private String name;
    }

}
