package com.mega.platform.cloud.data.vo.payment;

import com.mega.platform.cloud.data.vo.BaseReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

@Data
@Accessors(chain = true)
@Schema(description = "验证基础请求参数")
public class PaymentBaseReqVO extends BaseReqVO {

    @Schema(description = "设备UUID", required = true)
    @NotBlank(message = "设备UUID不能为空")
    private String deviceUuid;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "操作系统ID")
    private Integer deviceOsId;

    @Schema(description = "操作系统版本")
    private String deviceOsVersion;

    @Schema(description = "设备型号")
    private String deviceModel;

    @Schema(description = "应用版本号")
    private String bvrs;

    @Schema(description = "IP地址")
    private String ipAddress;

    @Schema(description = "网络类型")
    private String networkType;
}
