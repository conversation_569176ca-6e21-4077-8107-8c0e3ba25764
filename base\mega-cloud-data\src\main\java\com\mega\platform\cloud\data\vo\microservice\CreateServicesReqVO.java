package com.mega.platform.cloud.data.vo.microservice;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

@Data
@Schema(description = "创建服务请求参数")
public class CreateServicesReqVO {

    @Schema(description = "服务组ID")
    private Long serviceGroupId;

    @Schema(description = "目标ECS服务器ID")
    private Long targetEcsServerId;

    @Schema(description = "Jenkins参数")
    private Map<String, String> jenkinsParams;

    @Schema(description = "管理员用户ID")
    private Long adminUserId;

    @Schema(description = "服务名称")
    private String servicesName;
}
