package com.mega.platform.cloud.admin.vo;

import com.mega.platform.cloud.data.entity.JenkinsJobTemplateParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@Schema(description = "jenkins模板参数列表响应数据")
public class AdminBaseJenkinsTemplateParamListRespVO {

    @Schema(description = "jenkins模板参数列表")
    private List<JenkinsJobTemplateParam> jenkinsJobTemplateParams;
}
