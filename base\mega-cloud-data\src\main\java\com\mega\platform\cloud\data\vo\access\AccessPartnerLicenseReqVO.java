package com.mega.platform.cloud.data.vo.access;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

@Data
@Accessors(chain = true)
@Schema(name = "AccessPartnerLicenseReqVO", description = "合作方License验证请求参数")
public class AccessPartnerLicenseReqVO {

    @Schema(description = "appKey", example = "app_1752202145", required = true)
    @NotBlank(message = "appKey不能为空")
    private String appKey;

    @Schema(description = "服务名称", example = "gossipharbor-cloud-exchange", required = true)
    @NotBlank(message = "服务名称不能为空")
    private String serverName;

    @Schema(description = "内网IP地址", example = "*************", required = true)
    @NotBlank(message = "内网IP不能为空")
    private String innerIp;

    @Schema(description = "公网IP地址", example = "************", required = true)
    @NotBlank(message = "公网IP不能为空")
    private String publicIp;

    @Schema(description = "机器唯一标识", example = "machine-001", required = true)
    @NotBlank(message = "机器ID不能为空")
    private String machineId;

    @Schema(description = "License授权码", example = "LICENSE-123456", required = true)
    @NotBlank(message = "License不能为空")
    private String license;
}
