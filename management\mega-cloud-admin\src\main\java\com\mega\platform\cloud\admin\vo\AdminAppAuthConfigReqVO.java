package com.mega.platform.cloud.admin.vo;

import com.mega.platform.cloud.data.entity.AuthAliyunSmsConfig;
import com.mega.platform.cloud.data.entity.AuthAppleConfig;
import com.mega.platform.cloud.data.entity.AuthGoogleConfig;
import com.mega.platform.cloud.data.entity.AuthWeChatConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "应用auth配置")
public class AdminAppAuthConfigReqVO {

    @Schema(description = "应用ID")
    private Long projectAppId;

    @Schema(description = "第三方通用平台配置")
    private Long thirdPlatformId;

    @Schema(description = "阿里云短信配置")
    private AuthAliyunSmsConfig authAliyunSmsConfig;

    @Schema(description = "微信登录相关配置")
    private AuthWeChatConfig authWeChatConfig;

    @Schema(description = "谷歌登录相关配置")
    private AuthGoogleConfig authGoogleConfig;

    @Schema(description = "苹果登录相关配置")
    private AuthAppleConfig authAppleConfig;
}
