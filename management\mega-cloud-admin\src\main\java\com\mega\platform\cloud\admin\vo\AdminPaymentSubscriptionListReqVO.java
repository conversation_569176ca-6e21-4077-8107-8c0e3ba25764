package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "订阅记录分页查询参数")
public class AdminPaymentSubscriptionListReqVO {

    @Schema(description = "订单号")
    private Long orderId;

    @Schema(description = "三方平台编码")
    private String platformCode;

    @Schema(description = "订阅状态")
    private Integer status;

    @Schema(description = "页码，从1开始")
    private Integer pageNum = 1;

    @Schema(description = "每页数量，默认50")
    private Integer pageSize = 50;
}
