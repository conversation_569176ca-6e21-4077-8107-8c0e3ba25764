package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 权限管理-管理员与项目更新绑定请求
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-管理员与项目更新绑定请求")
public class AdminAccessUserProjectBindingUpdateReqVO {

    @Schema(description = "管理员ID", required = true)
    @NotNull(message = "管理员ID不能为空")
    private Long userId;

    @Schema(description = "项目ID", required = true)
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    @Schema(description = "删除标识: 0=未删除, 1=已删除", required = true)
    @NotNull(message = "删除标识不能为空")
    private Integer delsign;
}
