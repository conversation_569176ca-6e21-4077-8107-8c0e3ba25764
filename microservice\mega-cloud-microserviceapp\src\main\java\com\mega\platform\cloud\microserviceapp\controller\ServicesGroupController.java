package com.mega.platform.cloud.microserviceapp.controller;

import com.mega.platform.cloud.client.microservice.ServicesGroupClient;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.dto.microservice.BuildServicesDTO;
import com.mega.platform.cloud.data.vo.microservice.BuildServicesGroupReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesGroupReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesGroupRespVO;
import com.mega.platform.cloud.microservice.service.ServicesService;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Services Group 服务组接口")
@Slf4j
@RestController
public class ServicesGroupController implements ServicesGroupClient {

    private final ServicesService servicesService;

    @Autowired
    public ServicesGroupController(ServicesService servicesService) {
        this.servicesService = servicesService;
    }

    @Override
    @Operation(summary = "创建服务组")
    public Result<CreateServicesGroupRespVO> createServicesGroup(CreateServicesGroupReqVO vo) throws Exception {
        CreateServicesGroupRespVO respVO = servicesService.createServicesGroup(vo);
        return Results.success(respVO);
    }

    @Override
    @Operation(summary = "构建服务组")
    public Result<?> buildServicesGroup(BuildServicesGroupReqVO vo) throws Exception {
        BuildServicesDTO dto = new BuildServicesDTO();
        BeanUtils.copyProperties(vo, dto);
        servicesService.buildServicesGroup(dto);
        return Results.success();
    }
}