package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 权限管理-角色项目绑定列表请求参数
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-角色项目绑定列表请求参数")
public class AdminAccessRoleProjectBindingListReqVO {

    @Schema(description = "角色ID", example = "1")
    @NotNull(message = "角色ID不能为空")
    private Long adminRoleId;

    // @Schema(description = "页码", example = "1")
    // @NotNull(message = "页码不能为空")
    // @Min(value = 1, message = "页码必须大于0")
    // private Integer pageNum;

    // @Schema(description = "每页大小", example = "10")
    // @NotNull(message = "每页大小不能为空")
    // @Min(value = 1, message = "每页大小必须大于0")
    // private Integer pageSize;

    @Schema(description = "项目ID", example = "1")
    private Long projectId;

    @Schema(description = "删除标识(0=正常,1=删除)", example = "0")
    private Integer delsign;
}
