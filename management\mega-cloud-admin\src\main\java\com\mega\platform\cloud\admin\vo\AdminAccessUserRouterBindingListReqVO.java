package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 权限管理-管理员路由绑定列表请求
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-管理员路由绑定列表请求")
public class AdminAccessUserRouterBindingListReqVO {

    @Schema(description = "管理员ID", required = true)
    @NotNull(message = "管理员ID不能为空")
    private Long userId;

    // @Schema(description = "页码")
    // private Integer pageNum = 1;

    // @Schema(description = "每页大小")
    // private Integer pageSize = 10;

    @Schema(description = "路由ID")
    private Long routerId;

    @Schema(description = "删除标识: 0=未删除, 1=已删除")
    private Integer delsign;
}
