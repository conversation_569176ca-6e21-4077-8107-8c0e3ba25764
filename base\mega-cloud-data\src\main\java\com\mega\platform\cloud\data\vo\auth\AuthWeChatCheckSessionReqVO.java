package com.mega.platform.cloud.data.vo.auth;

import com.mega.platform.cloud.data.vo.BaseReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "微信会话校验请求参数")
public class AuthWeChatCheckSessionReqVO extends BaseReqVO {
    @Schema(description = "openid")
    private String openId;

    @Schema(description = "sessionKey")
    private String sessionKey;
}
