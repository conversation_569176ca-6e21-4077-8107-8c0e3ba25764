package com.mega.platform.cloud.auth.service;

import com.mega.platform.cloud.common.aop.RedisConsume;
import com.mega.platform.cloud.common.aop.RedisConsumeListener;
import com.mega.platform.cloud.common.mapper.AuthVerifyLogMapper;
import com.mega.platform.cloud.data.entity.AuthVerifyLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RedisConsume
@Slf4j
@RequiredArgsConstructor
public class RedisConsumeService {
    private final AuthVerifyLogMapper authVerifyLogMapper;

    @RedisConsumeListener(RedisProduceService.AUTH_VERIFY_LOG)
    @Transactional(rollbackFor = Exception.class)
    public void consumeAuthVerifyLog(AuthVerifyLog authVerifyLog) {
        authVerifyLogMapper.insertSelective(authVerifyLog);
    }
}
