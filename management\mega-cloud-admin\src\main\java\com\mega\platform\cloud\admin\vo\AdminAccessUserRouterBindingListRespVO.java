package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 权限管理 - 管理员路由绑定列表响应
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-管理员路由绑定列表响应")
public class AdminAccessUserRouterBindingListRespVO {

    @Schema(description = "管理员ID", example = "1001")
    private Long adminUserId;

    @Schema(description = "路由ID", example = "2001")
    private Long adminRouterId;

    @Schema(description = "删除标识: 0=未删除, 1=已删除", example = "0")
    private Integer delsign;
}
