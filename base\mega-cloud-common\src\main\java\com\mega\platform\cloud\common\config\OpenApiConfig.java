package com.mega.platform.cloud.common.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.util.ArrayList;
import java.util.List;

@Configuration
@Profile({"dev", "test", "prod"})
public class OpenApiConfig {

    private final ApiDocsProperties apiDocsProperties;

    // 从配置文件中获取上下文路径，默认为空
    @Value("${server.servlet.context-path:}")
    private String contextPath;

    @Value("${server.port:}")
    private String port;

    @Value("${spring.cloud.consul.discovery.service-name:}")
    private String serviceName;

    @Value("${spring.profiles.active:}")
    private String active;

    private static final String SECURITY_SCHEME_NAME_BEARER = "bearerAuth";

    private static final String SECURITY_SCHEME_NAME_ADMIN = "adminToken";

    public OpenApiConfig(ApiDocsProperties apiDocsProperties) {
        this.apiDocsProperties = apiDocsProperties;
    }

    @Bean
    public OpenAPI commonOpenAPI() {
        // 创建服务器列表
        List<Server> servers = new ArrayList<>();
        // 本地开发环境 - 使用相对路径
        servers.add(new Server()
                .url("http://localhost:"+port+contextPath) // 使用配置的上下文路径
                .description("本地开发环境"));

        // 测试环境
        servers.add(new Server()
                .url("http://*************:9921/" + serviceName.replace(active, "test") + contextPath)
                .description("测试环境"));

        // 测试环境
        servers.add(new Server()
                .url("http://*************:9921/" + serviceName.replace(active, "beta") + contextPath)
                .description("灰度环境"));

        // 生产环境
        servers.add(new Server()
                .url("http://***********:8000/" + serviceName.replace(active, "prod") + contextPath)
                .description("生产环境"));

        String securityScheme;
        Components components = new Components();
        if (serviceName.contains("admin")) {
            securityScheme = SECURITY_SCHEME_NAME_ADMIN;
            components = components.addSecuritySchemes(SECURITY_SCHEME_NAME_ADMIN,
                    new SecurityScheme()
                            .name(SECURITY_SCHEME_NAME_ADMIN)
                            .type(SecurityScheme.Type.APIKEY)
                            .in(SecurityScheme.In.HEADER));
        } else {
            securityScheme = SECURITY_SCHEME_NAME_BEARER;
            components = components.addSecuritySchemes("bearerAuth",
                    new SecurityScheme()
                            .type(SecurityScheme.Type.HTTP)
                            .scheme("bearer")
                            .bearerFormat("JWT"));
        }

        return new OpenAPI()
                .servers(servers)
                .info(new Info()
                        .title(apiDocsProperties.getTitle())
                        .version(apiDocsProperties.getVersion())
                        .description(apiDocsProperties.getDescription())
                        .contact(new Contact().name("Mega Platform")))
                .addSecurityItem(new SecurityRequirement().addList(securityScheme))
                .components(components);
    }

    @Bean
    public GroupedOpenApi commonOpenApiGroup() {
        return GroupedOpenApi.builder()
                .group("Mega Platform")
                .packagesToScan(apiDocsProperties.getApisPackage())
                .build();
    }
}
