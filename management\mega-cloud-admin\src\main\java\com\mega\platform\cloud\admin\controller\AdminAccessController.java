package com.mega.platform.cloud.admin.controller;

import com.mega.platform.cloud.admin.service.AdminAccessService;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.core.PageResult;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权限管理控制器
 */
@RestController
@Tag(name = "权限管理")
@Slf4j
public class AdminAccessController {

    private final AdminAccessService adminAccessService;

    @Autowired
    public AdminAccessController(AdminAccessService adminAccessService) {
        this.adminAccessService = adminAccessService;
    }

    // ==================== 用户管理 ====================

    @PostMapping("/system/access/user/list")
    @Operation(summary = "权限管理-管理员列表")
    public Result<List<AdminAccessUserListRespVO>> getUserList(@Validated @RequestBody AdminAccessUserListReqVO reqVO) {
        List<AdminAccessUserListRespVO> result = adminAccessService.getUserList(reqVO);
        return Results.success(result);
    }

    @PostMapping("/system/access/user/create")
    @Operation(summary = "权限管理-创建管理员")
    public Result<?> createUser(@Validated @RequestBody AdminAccessUserCreateReqVO reqVO) {
        adminAccessService.createUser(reqVO);
        return Results.success();
    }

    @PostMapping("/system/access/user/delete")
    @Operation(summary = "权限管理-删除管理员")
    public Result<?> deleteUser(@Validated @RequestBody AdminAccessUserDeleteReqVO reqVO) {
        adminAccessService.deleteUser(reqVO);
        return Results.success();
    }

    @PostMapping("/system/access/user/reset-password")
    @Operation(summary = "权限管理-重置管理员密码")
    public Result<?> resetUserPassword(@Validated @RequestBody AdminAccessUserResetPasswordReqVO reqVO) {
        adminAccessService.resetUserPassword(reqVO);
        return Results.success();
    }

    // ==================== 用户项目绑定管理 ====================

    @PostMapping("/system/access/user-project/binding/list")
    @Operation(summary = "权限管理-管理员项目绑定列表")
    public Result<List<AdminAccessUserProjectBindingListRespVO>> getUserProjectBindingList(
            @Validated @RequestBody AdminAccessUserProjectBindingListReqVO reqVO) {
        List<AdminAccessUserProjectBindingListRespVO> result = adminAccessService.getUserProjectBindingList(reqVO);
        return Results.success(result);
    }

    @PostMapping("/system/access/user-project/binding/update")
    @Operation(summary = "权限管理-更新管理员项目绑定")
    public Result<?> updateUserProjectBinding(@Validated @RequestBody AdminAccessUserProjectBindingUpdateReqVO reqVO) {
        adminAccessService.updateUserProjectBinding(reqVO);
        return Results.success();
    }

    // ==================== 用户路由绑定管理 ====================

    @PostMapping("/system/access/user-router/binding/list")
    @Operation(summary = "权限管理-管理员路由绑定列表")
    public Result<List<AdminAccessUserRouterBindingListRespVO>> getUserRouterBindingList(
            @Validated @RequestBody AdminAccessUserRouterBindingListReqVO reqVO) {
        List<AdminAccessUserRouterBindingListRespVO> result = adminAccessService.getUserRouterBindingList(reqVO);
        return Results.success(result);
    }

    @PostMapping("/system/access/user-router/binding/update")
    @Operation(summary = "权限管理-更新管理员路由绑定")
    public Result<?> updateUserRouterBinding(@Validated @RequestBody AdminAccessUserRouterBindingUpdateReqVO reqVO) {
        adminAccessService.updateUserRouterBinding(reqVO);
        return Results.success();
    }

    // ==================== 角色管理 ====================

    @PostMapping("/system/access/role/list")
    @Operation(summary = "权限管理-角色列表")
    public Result<List<AdminAccessRoleListRespVO>> getRoleList(@Validated @RequestBody AdminAccessRoleListReqVO reqVO) {
        List<AdminAccessRoleListRespVO> result = adminAccessService.getRoleList(reqVO);
        return Results.success(result);
    }

    @PostMapping("/system/access/role/create")
    @Operation(summary = "权限管理-创建角色")
    public Result<?> createRole(@Validated @RequestBody AdminAccessRoleCreateReqVO reqVO) {
        adminAccessService.createRole(reqVO);
        return Results.success();
    }

    @PostMapping("/system/access/role/delete")
    @Operation(summary = "权限管理-删除角色")
    public Result<?> deleteRole(@Validated @RequestBody AdminAccessRoleDeleteReqVO reqVO) {
        adminAccessService.deleteRole(reqVO);
        return Results.success();
    }

    // ==================== 角色路由绑定管理 ====================

    @PostMapping("/system/access/role-router/binding/list")
    @Operation(summary = "权限管理-角色路由绑定列表")
    public Result<List<AdminAccessRoleRouterBindingListRespVO>> getRoleRouterBindingList(
            @Validated @RequestBody AdminAccessRoleRouterBindingListReqVO reqVO) {
        List<AdminAccessRoleRouterBindingListRespVO> result = adminAccessService.getRoleRouterBindingList(reqVO);
        return Results.success(result);
    }

    @PostMapping("/system/access/role-router/binding/update")
    @Operation(summary = "权限管理-更新角色路由绑定")
    public Result<?> updateRoleRouterBinding(@Validated @RequestBody AdminAccessRoleRouterBindingUpdateReqVO reqVO) {
        adminAccessService.updateRoleRouterBinding(reqVO);
        return Results.success();
    }

    // ==================== 角色项目绑定管理 ====================

    @PostMapping("/system/access/role-project/binding/list")
    @Operation(summary = "权限管理-角色项目绑定列表")
    public Result<List<AdminAccessRoleProjectBindingListRespVO>> getRoleProjectBindingList(
            @Validated @RequestBody AdminAccessRoleProjectBindingListReqVO reqVO) {
        List<AdminAccessRoleProjectBindingListRespVO> result = adminAccessService.getRoleProjectBindingList(reqVO);
        return Results.success(result);
    }

    @PostMapping("/system/access/role-project/binding/update")
    @Operation(summary = "权限管理-更新角色项目绑定")
    public Result<?> updateRoleProjectBinding(@Validated @RequestBody AdminAccessRoleProjectBindingUpdateReqVO reqVO) {
        adminAccessService.updateRoleProjectBinding(reqVO);
        return Results.success();
    }

    // ==================== 角色用户绑定管理 ====================

    @PostMapping("/system/access/role-user/binding/list")
    @Operation(summary = "权限管理-角色用户绑定列表")
    public Result<List<AdminAccessRoleUserBindingListRespVO>> getRoleUserBindingList(
            @Validated @RequestBody AdminAccessRoleUserBindingListReqVO reqVO) {
        List<AdminAccessRoleUserBindingListRespVO> result = adminAccessService.getRoleUserBindingList(reqVO);
        return Results.success(result);
    }

    @PostMapping("/system/access/role-user/binding/update")
    @Operation(summary = "权限管理-更新角色用户绑定")
    public Result<?> updateRoleUserBinding(@Validated @RequestBody AdminAccessRoleUserBindingUpdateReqVO reqVO) {
        adminAccessService.updateRoleUserBinding(reqVO);
        return Results.success();
    }

    // ==================== 路由管理 ====================

    @PostMapping("/system/access/router/list")
    @Operation(summary = "权限管理-路由列表")
    public Result<List<AdminAccessRouterListRespVO>> getRouterList(@Validated @RequestBody AdminAccessRouterListReqVO reqVO) {
        List<AdminAccessRouterListRespVO> result = adminAccessService.getRouterList(reqVO);
        return Results.success(result);
    }

}
