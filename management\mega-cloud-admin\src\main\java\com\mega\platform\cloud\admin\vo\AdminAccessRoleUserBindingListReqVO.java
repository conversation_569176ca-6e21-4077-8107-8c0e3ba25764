package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 权限管理-角色用户绑定列表请求参数
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-角色用户绑定列表请求参数")
public class AdminAccessRoleUserBindingListReqVO {

    @Schema(description = "角色ID", required = true, example = "1")
    @NotNull(message = "角色ID不能为空")
    private Long adminRoleId;

    // 注释的分页字段，如需使用可以打开并加上注解
    /*
    @Schema(description = "页码", required = true, example = "1")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum;

    @Schema(description = "每页大小", required = true, example = "10")
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize;
    */

    @Schema(description = "用户ID", example = "1")
    private Long adminUserId;

    @Schema(description = "删除标识(0=正常,1=删除)", example = "0")
    private Integer delsign;
}
