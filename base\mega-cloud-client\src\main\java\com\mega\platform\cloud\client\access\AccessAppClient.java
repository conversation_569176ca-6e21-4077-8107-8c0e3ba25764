package com.mega.platform.cloud.client.access;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.annotation.DescriptionTag;
import com.mega.platform.cloud.data.vo.access.AccessAppTokenReqVO;
import com.mega.platform.cloud.data.vo.access.AccessAppTokenRespVO;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(
        value = "mega-cloud-access-" + "${spring.profiles.active}",
        contextId = "mega-cloud-access-app-client",
        path = "/access/api")
@DescriptionTag(name = "应用访问接口")
public interface AccessAppClient {
    @Operation(summary = "获取登录token")
    @PostMapping("/app/public/token")
    Result<AccessAppTokenRespVO> accessAppToken(@Validated @RequestBody AccessAppTokenReqVO vo);
}
