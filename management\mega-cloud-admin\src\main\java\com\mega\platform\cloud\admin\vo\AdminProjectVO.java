package com.mega.platform.cloud.admin.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 项目详情响应参数
 */
@Data
@Accessors(chain = true)
@Schema(name = "AdminProjectVO", description = "项目详情响应参数")
public class AdminProjectVO {

    @Schema(description = "项目ID")
    private Long id;

    @Schema(description = "项目名称")
    private String name;

    @Schema(description = "状态 0：不可用 1：正常 2：挂起 3：审核中")
    private Integer status;

    @Schema(description = "状态描述")
    private String statusDesc;

    @Schema(description = "描述")
    private String remark;

    @Schema(description = "创建时间")
    private Date createTime;
}
