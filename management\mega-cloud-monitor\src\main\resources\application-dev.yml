monitor:
  topic:
    metrics-ecs-collect: metrics-ecs-collect-${spring.profiles.active}
    metrics-services-collect: metrics-services-collect-${spring.profiles.active}
  collection:
    metrics-ecs-server-data: metrics_ecs_server_data_${spring.profiles.active}
    metrics-ecs-biz-data: metrics_ecs_biz_data_${spring.profiles.active}
    metrics-services-server-data: metrics_services_server_data_${spring.profiles.active}
    metrics-services-biz-data: metrics_services_biz_data_${spring.profiles.active}