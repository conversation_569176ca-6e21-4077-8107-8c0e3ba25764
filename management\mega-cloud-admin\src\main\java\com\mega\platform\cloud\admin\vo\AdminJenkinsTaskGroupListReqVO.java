package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 查询Jenkins任务组列表请求参数
 */
@Data
@Accessors(chain = true)
@Schema(name = "查询Jenkins任务组列表请求参数", description = "查询Jenkins任务组列表请求参数")
public class AdminJenkinsTaskGroupListReqVO {

    @NotNull(message = "服务组ID不能为空")
    @Schema(description = "服务组ID", required = true, example = "17")
    private Long servicesGroupId;
}
