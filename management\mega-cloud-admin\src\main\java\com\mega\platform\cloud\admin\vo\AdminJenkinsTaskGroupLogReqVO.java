package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 查询Jenkins任务组日志请求参数
 */
@Data
@Accessors(chain = true)
@Schema(name = "查询Jenkins任务组日志请求参数", description = "查询Jenkins任务组日志请求参数")
public class AdminJenkinsTaskGroupLogReqVO {

    @NotNull(message = "任务组ID不能为空")
    @Schema(description = "任务组ID", required = true, example = "27")
    private Long jenkinsTaskGroupId;
}
