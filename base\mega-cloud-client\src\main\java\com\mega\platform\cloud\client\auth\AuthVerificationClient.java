package com.mega.platform.cloud.client.auth;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.annotation.DescriptionTag;
import com.mega.platform.cloud.data.vo.auth.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(
        value = "mega-cloud-auth-" + "${spring.profiles.active}",
        contextId = "mega-cloud-auth-verification-client",
        path = "/auth/api")
@DescriptionTag(name = "验证码相关接口")
public interface AuthVerificationClient {

    @Operation(summary = "获取手机验证码")
    @PostMapping("/verification/sms/code")
    Result<AuthSendSmsCodeRespVO> sendSmsCode(@Validated @RequestBody AuthSendSmsCodeReqVO vo) throws Exception;

    @Operation(summary = "验证手机验证码")
    @PostMapping("/verification/sms/verify")
    Result<Boolean> verifySmsCode(@Validated @RequestBody AuthVerifySmsCodeReqVO vo);

    @Operation(summary = "验证苹果")
    @PostMapping("/verification/apple/verify")
    Result<AuthVerifyAppleTokenRespVO> verifyAppleToken(@Validated @RequestBody AuthVerifyAppleTokenReqVO vo);

    @Operation(summary = "验证微信code")
    @PostMapping("/verification/wechat/verify")
    Result<AuthWeChatUserInfoRespVO> verifyWechatCode(@Validated @RequestBody AuthWeChatUserInfoReqVO vo);

    @Operation(summary = "验证微信小程序code")
    @PostMapping("/verification/wechat/mini/verify")
    Result<AuthWeChatMiniUserInfoRespVO> verifyWechatMiniCode(@Validated @RequestBody AuthWeChatUserInfoReqVO vo);

    @Operation(summary = "微信小程序检验登录态")
    @PostMapping("/verification/wechat/mini/check/session")
    Result<Boolean> WechatMiniCodeCheckSession(@Validated @RequestBody AuthWeChatCheckSessionReqVO vo);

    @Operation(summary = "验证抖音小程序code")
    @PostMapping("/verification/douyin/mini/verify")
    Result<AuthDouyinMiniUserInfoRespVO> verifyDouyinMiniCode(AuthWeChatUserInfoReqVO vo);
}
