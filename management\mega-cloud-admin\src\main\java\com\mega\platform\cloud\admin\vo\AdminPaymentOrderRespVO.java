package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "支付订单响应")
public class AdminPaymentOrderRespVO {

    @Schema(description = "订单主键ID", example = "10001")
    private Long id;

    @Schema(description = "支付平台订单号", example = "ALI202507221234567890")
    private String orderNo;

    @Schema(description = "外部订单号（中台生成）", example = "OUT202507221111")
    private String outOrderNo;

    @Schema(description = "订单总金额", example = "68.00")
    private BigDecimal totalAmount;

    @Schema(description = "币种", example = "CNY")
    private String currency;

    @Schema(description = "订单状态（0=初始化, 1=成功, 2=失败, 3=已退款）", example = "1")
    private Integer status;

    @Schema(description = "支付完成时间", example = "2025-07-22T14:30:00")
    private LocalDateTime payTime;

    @Schema(description = "支付平台代码", example = "APPLE")
    private String platformCode;

    @Schema(description = "订单类型", example = "SUBSCRIPTION")
    private String orderType;
}
