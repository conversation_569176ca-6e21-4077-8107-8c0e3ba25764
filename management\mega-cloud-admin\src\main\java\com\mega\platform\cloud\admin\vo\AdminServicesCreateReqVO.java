package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Map;

@Data
@Accessors(chain = true)
@Schema(name = "服务创建请求参数", description = "服务创建请求参数")
public class AdminServicesCreateReqVO {

    @NotNull(message = "微服务组id不能为空")
    @Schema(description = "微服务组id，不能为空", required = true)
    private Long serviceGroupId;

    @NotNull(message = "目标服务器id不能为空")
    @Schema(description = "目标服务器id（ecs_server_id），不能为空", required = true)
    private Long targetEcsServerId;

    @Schema(description = "Jenkins 构建参数")
    private Map<String, String> jenkinsParams;

    @NotNull(message = "操作人admin用户id不能为空")
    @Schema(description = "操作人admin用户id", required = true)
    private Long adminUserId;

    @NotNull(message = "微服务名称不能为空")
    @Schema(description = "微服务名称，不能为空", required = true)
    private String servicesName;

    @Schema(description = "程序路径")
    private String path;

    @Schema(description = "日志路径")
    private String logPath;

    @Schema(description = "服务描述")
    private String description;

    @Schema(description = "日志超时时间（秒）")
    private Integer logTimeoutSecond;

    @Schema(description = "排序字段")
    private Integer sort;

    @Schema(description = "备注信息")
    private String remark;
}
