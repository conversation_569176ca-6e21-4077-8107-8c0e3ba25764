package com.mega.platform.cloud.data.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "PaymentWechatConfig", description = "微信支付平台配置")
public class PaymentWechatConfig {

    @Schema(description = "微信支付 API V3 密钥", example = "your-api-v3-key")
    private String apiV3Key;

    @Schema(description = "微信商户号", example = "1234567890")
    private String merchantId;

    @Schema(description = "商户私钥文件路径（.pem 文件）", example = "/path/to/private_key.pem")
    private String privateKeyPath;

    @Schema(description = "微信平台证书序列号", example = "1234567890ABCDEF")
    private String merchantSerialNumber;

    @Schema(description = "微信开放平台申请的 AppID", example = "wx1234567890abcdef")
    private String appId;

    @Schema(description = "支付成功通知地址", example = "https://example.com/payment/success")
    private String noticeUrl;
}
