package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "订阅记录响应")
public class AdminPaymentSubscriptionRespVO {

    @Schema(description = "订阅记录ID", example = "123456")
    private Long id;

    @Schema(description = "主订单号", example = "987654321")
    private Long parentOrderNo;

    @Schema(description = "三方平台", example = "APPLE")
    private String platformCode;

    @Schema(description = "订阅状态", example = "1")
    private Integer status;

    @Schema(description = "下次扣费时间", example = "2025-08-15T14:00:00")
    private LocalDateTime nextChargeTime;

    @Schema(description = "创建时间", example = "2025-07-01T10:00:00")
    private LocalDateTime createTime;
}
