package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 权限管理-角色路由绑定更新请求参数
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-角色路由绑定更新请求参数")
public class AdminAccessRoleRouterBindingUpdateReqVO {

    @Schema(description = "角色ID", example = "1")
    @NotNull(message = "角色ID不能为空")
    private Long adminRoleId;

    @Schema(description = "路由ID", example = "1")
    @NotNull(message = "路由ID不能为空")
    private Long adminRouterId;

    @Schema(description = "删除标识: 0=未删除, 1=已删除", example = "0")
    @NotNull(message = "删除标识不能为空")
    private Integer delsign;

}
