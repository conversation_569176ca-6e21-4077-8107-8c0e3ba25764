package com.mega.platform.cloud.admin.vo;

import com.mega.platform.cloud.data.entity.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "应用支付配置")
public class AdminAppPaymentConfigReqVO {

    @Schema(description = "应用ID")
    private Long projectAppId;

    @Schema(description = "第三方通用平台配置")
    private Long thirdPlatformId;

    @Schema(description = "阿里支付配置")
    private PaymentAlipayConfig paymentAlipayConfig;

    @Schema(description = "微信支付配置")
    private PaymentWechatConfig paymentWechatConfig;

    @Schema(description = "苹果支付配置")
    private PaymentAppleConfig paymentAppleConfig;
}
