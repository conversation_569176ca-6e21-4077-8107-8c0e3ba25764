package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "服务组列表返回项")
public class AdminServicesGroupRespVO {

    @Schema(description = "服务组ID")
    private Long servicesGroupId;

    @Schema(description = "服务组名称")
    private String servicesGroupName;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "管理员信息")
    private String adminUserId;

    @Schema(description = "是否自研")
    private Integer isSelf;

    @Schema(description = "上线状态")
    private Integer onlineStatus;

    @Schema(description = "运行状态")
    private Integer runStatus;

    @Schema(description = "真实运行状态")
    private Integer realRunStatus;

    @Schema(description = "标签列表 逗号分隔")
    private String tagIds; // 标签ID列表
}
