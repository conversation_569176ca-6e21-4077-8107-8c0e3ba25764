package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 查询服务最后一次任务响应参数
 */
@Data
@Accessors(chain = true)
@Schema(name = "查询服务最后一次任务响应参数", description = "查询服务最后一次任务响应参数")
public class AdminServicesLastTaskRespVO {

    // 服务基本信息
    @Schema(description = "服务ID")
    private Long servicesId;

    @Schema(description = "服务名称")
    private String servicesName;

    @Schema(description = "服务备注")
    private String servicesRemark;

    @Schema(description = "服务状态")
    private Integer servicesStatus;

    @Schema(description = "服务运行状态")
    private Integer servicesRunningStatus;

    @Schema(description = "服务真实运行状态")
    private Integer servicesRealRunningStatus;

    @Schema(description = "服务创建时间")
    private Date serviceCreateTime;

    // 最后一次任务信息
    @Schema(description = "最后一次任务ID")
    private Long lastTaskId;

    @Schema(description = "最后一次任务操作类型")
    private Integer lastTaskAction;

    @Schema(description = "最后一次任务是否成功")
    private Integer lastTaskIsSuccess;

    @Schema(description = "最后一次任务失败原因")
    private String lastFailedReason;

    @Schema(description = "最后一次任务Git提交")
    private String lastGitCommit;

    @Schema(description = "最后一次任务Jenkins Job ID")
    private Long lastJenkinsJobId;

    @Schema(description = "最后一次任务Jenkins Job URL")
    private String lastJenkinsJobUrl;

    @Schema(description = "最后一次任务Jenkins任务组ID")
    private Long lastJenkinsTaskGroupId;

    @Schema(description = "最后一次任务请求数据")
    private String lastTaskRequestData;

    @Schema(description = "最后一次任务备注")
    private String lastTaskRemark;

    @Schema(description = "最后一次任务完成时间")
    private Date lastTaskCompleteTime;
}
