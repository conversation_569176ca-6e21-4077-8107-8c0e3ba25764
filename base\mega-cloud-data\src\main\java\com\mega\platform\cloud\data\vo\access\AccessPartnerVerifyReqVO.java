package com.mega.platform.cloud.data.vo.access;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(name = "AccessPartnerVerifyReqVO", description = "合作方AES密钥获取请求参数")
public class AccessPartnerVerifyReqVO {

    @Schema(description = "appKey", example = "app_1752202145", required = true)
    @NotBlank(message = "appKey不能为空")
    private String appKey;

    @Schema(description = "服务名称", example = "gossipharbor-cloud-exchange", required = true)
    @NotBlank(message = "服务名称不能为空")
    private String serverName;

    @Schema(description = "文件MD5列表", required = true)
    @NotEmpty(message = "MD5列表不能为空")
    @Valid
    private List<AccessMd5VO> md5List;
}
