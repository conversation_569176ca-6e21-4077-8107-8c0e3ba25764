package com.mega.platform.cloud.admin.vo;

import com.mega.platform.cloud.data.entity.Dic;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
@Schema(description = "dic列表响应数据")
public class AdminBaseDicListRespVO {

    @Schema(description = "按分类ID分组的字典列表，key为分类ID")
    private Map<Long, List<Dic>> dicMap;
}
