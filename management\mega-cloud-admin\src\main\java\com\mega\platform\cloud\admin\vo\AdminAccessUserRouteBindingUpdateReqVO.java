package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 权限管理-管理员路由更新绑定请求
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-管理员路由更新绑定请求")
public class AdminAccessUserRouteBindingUpdateReqVO {

    @Schema(description = "管理员ID", required = true, example = "1001")
    @NotNull(message = "管理员ID不能为空")
    private Long adminUserId;

    @Schema(description = "路由ID", required = true, example = "2001")
    @NotNull(message = "路由ID不能为空")
    private Long adminRouterId;

    @Schema(description = "删除标识", required = true, example = "0")
    @NotNull(message = "删除标识不能为空")
    private Integer delsign;
}
