package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 权限管理 - 管理员项目绑定列表请求
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-管理员项目绑定列表请求")
public class AdminAccessUserProjectBindingListReqVO {

    @Schema(description = "管理员ID", required = true, example = "1001")
    @NotNull(message = "管理员ID不能为空")
    private Long adminUserId;

    @Schema(description = "删除标识: 0=未删除, 1=已删除", example = "0")
    private Integer delsign;
}
