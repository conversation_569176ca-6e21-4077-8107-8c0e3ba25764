package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 管理员登录请求参数
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "管理员登录请求参数")
public class AdminAuthLoginReqVO {

    @Schema(description = "账号", example = "admin")
    private String username;

    @Schema(description = "密码", example = "123456")
    private String password;

}
