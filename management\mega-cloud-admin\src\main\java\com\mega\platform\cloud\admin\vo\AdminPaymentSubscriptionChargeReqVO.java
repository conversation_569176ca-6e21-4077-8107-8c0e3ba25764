package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "订阅充值分页查询参数")
public class AdminPaymentSubscriptionChargeReqVO {

    @Schema(description = "订单号", example = "123456789")
    private Long orderNo;

    @Schema(description = "订阅表id", example = "987654321")
    private Long paymentSubscriptionId;

    @Schema(description = "页码，从1开始", example = "1")
    private Integer pageNum = 1;

    @Schema(description = "每页数量，默认50", example = "50")
    private Integer pageSize = 50;
}
