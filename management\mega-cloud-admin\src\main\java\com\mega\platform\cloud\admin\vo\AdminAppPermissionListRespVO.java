package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@Schema(description = "App权限列表响应数据")
public class AdminAppPermissionListRespVO {

    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Schema(description = "所属App的ID", example = "1")
    private Long projectAppId;

    @Schema(description = "所属App的名称", example = "auth")
    private String projectAppName;

    @Schema(description = "Ant风格路径匹配", example = "/api/v1/user/**")
    private String urlPattern;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "是否删除：0=未删除，1=已删除", example = "0")
    private Boolean delsign;
}
