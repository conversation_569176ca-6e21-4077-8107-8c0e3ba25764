package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@Schema(description = "运行中服务组及最近一次任务返回项")
public class AdminServicesGroupRunningJobRespVO {

    @Schema(description = "服务组ID")
    private Long servicesGroupId;

    @Schema(description = "服务组名称")
    private String servicesGroupName;

    @Schema(description = "产品ID")
    private Long projectId;

    @Schema(description = "AppID")
    private Long projectAppId;

    @Schema(description = "环境")
    private String servicesEnv;

    @Schema(description = "管理员ID")
    private Long adminUserId;

    @Schema(description = "最近一次任务ID")
    private Long jenkinsTaskGroupId;

    @Schema(description = "最近一次任务操作类型")
    private Integer action;

    @Schema(description = "最近一次任务是否成功")
    private Integer isSuccess;

    @Schema(description = "最近一次任务失败原因")
    private String failedReason;

    @Schema(description = "最近一次任务完成时间")
    private LocalDateTime completeTime;
}
