package com.mega.platform.cloud.data.vo.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "AuthSendSmsCodeRespVO", description = "发送短信验证码响应")
public class AuthSendSmsCodeRespVO {

    @Schema(description = "验证码有效时间（秒）", example = "600")
    private int expireSecond;

    @Schema(description = "验证码，仅测试/开发环境返回", example = "123456")
    private String code;
}
