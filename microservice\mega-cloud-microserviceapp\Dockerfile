FROM eclipse-temurin:11-alpine
ARG JAR_FILE
ENV MEM 512m
ENV CONFIG ""
ENV JAR_FILE=${JAR_FILE}
WORKDIR /opt
RUN sed -i "s/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g" /etc/apk/repositories
RUN apk add tzdata \
    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata \
    && apk add --update ttf-dejavu fontconfig
ADD target/${JAR_FILE} ${JAR_FILE}
ENTRYPOINT exec java -Xms${MEM} -Xmx${MEM} -jar ${JAR_FILE} ${CONFIG}
