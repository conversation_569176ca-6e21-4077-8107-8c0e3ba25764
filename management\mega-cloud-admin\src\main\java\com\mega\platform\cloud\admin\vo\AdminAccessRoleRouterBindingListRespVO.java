package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 权限管理-角色路由绑定列表响应参数
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-角色路由绑定列表响应参数")
public class AdminAccessRoleRouterBindingListRespVO {

    @Schema(description = "角色ID", example = "1")
    private Long adminRoleId;

    @Schema(description = "路由ID", example = "1")
    private Long adminRouterId;

    @Schema(description = "路由名称", example = "1")
    private String routerName;

    @Schema(description = "删除标识(0=正常,1=删除)", example = "0")
    private Integer delsign;
}
