package com.mega.platform.cloud.data.dto.monitor;

import com.mega.platform.cloud.data.entity.MonitorNotify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "MetricsNotifyDTO", description = "监控告警通知 DTO")
public class MetricsNotifyDTO extends MonitorNotify {

    @Schema(description = "指标名称")
    private String metricsName;

    @Schema(description = "规则名称")
    private String ruleName;

    @Schema(description = "服务名称")
    private String servicesName;

    @Schema(description = "服务分组名称")
    private String servicesGroupName;

    @Schema(description = "服务ID")
    private Long servicesId;

    @Schema(description = "来源类型")
    private Integer sourceType;

    @Schema(description = "ECS ID")
    private Long ecsId;

    @Schema(description = "ECS 名称")
    private String ecsName;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "告警等级")
    private Integer level;
}
