package com.mega.platform.cloud.data.dto.monitor;

import com.mega.platform.cloud.data.entity.MonitorRule;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "MetricsRuleDTO", description = "监控规则指标 DTO")
public class MetricsRuleDTO extends MonitorRule {

    @Schema(description = "指标键", example = "cpu.usage")
    private String metricsKey;

    @Schema(description = "指标类型", example = "1")
    private Integer metricsType;
}
