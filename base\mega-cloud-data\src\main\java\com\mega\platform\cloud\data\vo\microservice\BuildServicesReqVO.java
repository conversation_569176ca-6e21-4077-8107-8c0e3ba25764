package com.mega.platform.cloud.data.vo.microservice;

import com.mega.platform.cloud.data.vo.BaseReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "构建服务请求参数")
public class BuildServicesReqVO {

    @Schema(description = "服务ID")
    private Long servicesId;

    @Schema(description = "操作类型")
    private Integer action;

    @Schema(description = "管理员用户ID")
    private Long adminUserId;
}
