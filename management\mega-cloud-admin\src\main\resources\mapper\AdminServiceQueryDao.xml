<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.platform.cloud.admin.dao.AdminServiceQueryDao">

    <!-- 查询全部services列表 -->
    <select id="selectServicesList" resultType="com.mega.platform.cloud.admin.dto.AdminServicesDetailDTO">
        SELECT 
            s.id as servicesId,
            s.name as servicesName,
            s.remark as servicesRemark,
            s.status as servicesStatus,
            s.running_status as servicesRunningStatus,
            s.real_running_status as servicesRealRunningStatus,

            ltg.id as lastTaskGroupId,
            ltg.action as lastTaskGroupAction,
            ltg.is_success as lastTaskGroupIsSuccess,
            ltg.complete_time as lastTaskGroupCompleteTime,

            lt.id as lastTaskId,
            lt.action as lastTaskAction,
            lt.is_success as lastTaskIsSuccess,
            lt.complete_time as lastTaskCompleteTime,
            lt.jenkins_job_url as lastTaskJenkinsJobUrl,

            sg.*
        FROM services s
        LEFT JOIN services_group sg ON s.services_group_id = sg.id AND sg.delsign = 0
        LEFT JOIN (
            SELECT services_group_id, MAX(id) as max_id
            FROM jenkins_task_group 
            WHERE delsign = 0
            GROUP BY services_group_id
        ) ltg_max ON sg.id = ltg_max.services_group_id
        LEFT JOIN jenkins_task_group ltg ON ltg_max.max_id = ltg.id
        LEFT JOIN (
            SELECT jenkins_job_id, MAX(id) as max_id
            FROM jenkins_task 
            WHERE delsign = 0
            GROUP BY jenkins_job_id
        ) lt_max ON s.jenkins_job_id = lt_max.jenkins_job_id
        LEFT JOIN jenkins_task lt ON lt_max.max_id = lt.id
        WHERE  s.delsign = 0
        <if test="projectId != null">
            AND sg.project_id = #{projectId} 
        </if>
        <if test="reqVO.servicesGroupId != null">
            AND s.services_group_id = #{reqVO.servicesGroupId}
        </if>
        <if test="reqVO.servicesName != null and reqVO.servicesName != ''">
            AND s.name LIKE CONCAT('%', #{reqVO.servicesName}, '%')
        </if>
        <if test="reqVO.remark != null and reqVO.remark != ''">
            AND s.remark LIKE CONCAT('%', #{reqVO.remark}, '%')
        </if>
        <if test="reqVO.adminUserId != null">
            AND sg.admin_user_id = #{reqVO.adminUserId}
        </if>
        <if test="reqVO.status != null">
            AND s.status = #{reqVO.status}
        </if>
        <if test="reqVO.runningStatus != null">
            AND s.running_status = #{reqVO.runningStatus}
        </if>
        <if test="reqVO.realRunningStatus != null">
            AND s.real_running_status = #{reqVO.realRunningStatus}
        </if>
        <if test="reqVO.tags != null and reqVO.tags.size() > 0">
            AND sg.id IN (
                SELECT DISTINCT sgtr.service_group_id 
                FROM services_group_tag_relation sgtr 
                WHERE sgtr.delsign = 0 
                AND sgtr.service_tag_id IN
                <foreach collection="reqVO.tags" item="tagId" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
            )
        </if>
        ORDER BY sg.id DESC
    </select>

    <!-- 基于组查询services列表 -->
    <select id="selectServicesListByGroup" resultType="com.mega.platform.cloud.admin.vo.AdminServicesListByGroupRespVO">
        SELECT 
            s.id as servicesId,
            s.name as servicesName,
            s.remark as servicesRemark,
            s.status as servicesStatus,
            s.running_status as servicesRunningStatus,
            s.real_running_status as servicesRealRunningStatus,
            s.create_time as servicesCreateTime,
            lt.id as lastTaskId,
            lt.action as lastTaskAction,
            lt.is_success as lastTaskIsSuccess,
            lt.complete_time as lastTaskCompleteTime,
            lt.jenkins_job_url as lastTaskJenkinsJobUrl,
            lt.git_commit as lastTaskGitCommit
        FROM services s
        LEFT JOIN services_group as sg ON sg.id = s.services_group_id
        LEFT JOIN (
            SELECT jenkins_job_id, MAX(id) as max_id
            FROM jenkins_task 
            WHERE delsign = 0
            GROUP BY jenkins_job_id
        ) lt_max ON s.jenkins_job_id = lt_max.jenkins_job_id
        LEFT JOIN jenkins_task lt ON lt_max.max_id = lt.id
        WHERE sg.project_id = #{projectId} 
        AND s.services_group_id = #{servicesGroupId}
        AND s.delsign = 0
        ORDER BY s.create_time DESC
    </select>

    <!-- selectServicesTags --> 

    <select id="selectServicesTags" resultType="com.mega.platform.cloud.admin.dto.AdminServicesTagDTO">
    SELECT 
        sgt.service_group_id as serviceGroupId,
        sgt.service_tag_id as tagId
        FROM services_group_tag_relation AS sgt
        WHERE sgt.service_group_id in 
        <foreach collection="servicesGroupIds" item="servicesGroupId" open="(" separator="," close=")">
            #{servicesGroupId}
        </foreach>
        AND sgt.delsign = 0;
    </select>

    <!-- selectServicesParams --> 

    <select id="selectServicesParams" resultType="com.mega.platform.cloud.admin.dto.AdminServicesJenkinsParamDTO">
    SELECT 
        s.id,
        s.`name`,
        jp.param_key as paramKey,
        jpv.param_value as paramValue
        FROM jenkins_job_template_param_value as jpv
        LEFT JOIN jenkins_job_template_param as jp ON jpv.jenkins_job_templete_param_id = jp.id
        LEFT JOIN services as s ON jpv.services_data_id = s.id
        WHERE
        jpv.services_data_type = 2
        AND s.id in 
        <foreach collection="servicesIds" item="servicesId" open="(" separator="," close=")">
            #{servicesId}
        </foreach>
        AND s.delsign = 0
        AND jpv.delsign = 0;
    </select>

    <!-- selectServicesGroupParams --> 

    <select id="selectServicesGroupParams" resultType="com.mega.platform.cloud.admin.dto.AdminServicesJenkinsParamDTO">
    SELECT 
        sg.id,
        sg.`name`,
        jp.param_key as paramKey,
        jpv.param_value as paramValue
        FROM jenkins_job_template_param_value as jpv
        LEFT JOIN jenkins_job_template_param as jp ON jpv.jenkins_job_templete_param_id = jp.id
        LEFT JOIN services_group as sg ON jpv.services_data_id = sg.id
        WHERE
        jpv.services_data_type = 1
        AND sg.id in 
        <foreach collection="servicesGroupIds" item="servicesGroupId" open="(" separator="," close=")">
            #{servicesGroupId}
        </foreach>
        AND sg.delsign = 0
        AND jpv.delsign = 0;
    </select>

    <!-- 查询服务最后一次任务 -->
    <select id="selectServicesLastTask" resultType="com.mega.platform.cloud.admin.vo.AdminServicesLastTaskRespVO">
        SELECT
            s.id                  AS servicesId,
            s.name                AS servicesName,
            s.remark              AS servicesRemark,
            s.status              AS servicesStatus,
            s.running_status      AS servicesRunningStatus,
            s.real_running_status AS servicesRealRunningStatus,
            s.create_time         AS serviceCreateTime,
            lt.id                 AS lastTaskId,
            lt.action             AS lastTaskAction,
            lt.is_success         AS lastTaskIsSuccess,
            lt.failed_reason      AS lastFailedReason,
            lt.git_commit         AS lastGitCommit,
            lt.jenkins_job_id     AS lastJenkinsJobId,
            lt.jenkins_job_url    AS lastJenkinsJobUrl,
            lt.jenkins_task_group_id AS lastJenkinsTaskGroupId,
            lt.request_data       AS lastTaskRequestData,
            lt.remark             AS lastTaskRemark,
            lt.complete_time      AS lastTaskCompleteTime
        FROM services AS s
        LEFT JOIN services_group AS sg ON sg.id = s.services_group_id
        LEFT JOIN (
            SELECT jenkins_job_id, MAX(id) AS max_id
            FROM jenkins_task
            WHERE delsign = 0
            GROUP BY jenkins_job_id
        ) lt_max ON s.jenkins_job_id = lt_max.jenkins_job_id
        LEFT JOIN jenkins_task lt ON lt_max.max_id = lt.id
        WHERE
        s.delsign = 0
        AND sg.project_id = #{projectId}
        AND sg.delsign = 0
        AND s.id = #{servicesId}
        AND (lt.delsign = 0 OR lt.delsign IS NULL)
    </select>

    <!-- 查询Jenkins任务组列表 -->
    <select id="selectJenkinsTaskGroupList" resultType="com.mega.platform.cloud.admin.vo.AdminJenkinsTaskGroupListRespVO">
        SELECT
        jtg.*
        FROM jenkins_task_group AS jtg
        LEFT JOIN services_group AS sg ON jtg.services_group_id = sg.id
        WHERE sg.project_id = #{projectId}
        AND jtg.services_group_id = #{servicesGroupId}
        AND jtg.delsign = 0
        AND sg.delsign = 0
        ORDER BY jtg.id DESC
    </select>

    <!-- 查询Jenkins任务组日志 -->
    <select id="selectJenkinsTaskGroupLog" resultType="com.mega.platform.cloud.admin.dto.AdminJenkinsTaskLogDTO">
        SELECT
        jtl.jenkins_task_group_id AS jenkinsTaskGroupId,
        jtl.jenkins_task_id AS jenkinsTaskId,
        jtl.log_time AS logTime,
        jtl.log_content AS logContent
        FROM jenkins_task_log AS jtl
        LEFT JOIN jenkins_task_group AS jtg ON jtg.id = jtl.jenkins_task_group_id
        LEFT JOIN services_group AS sg ON jtg.services_group_id = sg.id
        WHERE
        jtl.jenkins_task_group_id = #{jenkinsTaskGroupId}
        AND sg.project_id = #{projectId}
        ORDER BY jtl.jenkins_task_id, jtl.log_time
    </select>
</mapper>