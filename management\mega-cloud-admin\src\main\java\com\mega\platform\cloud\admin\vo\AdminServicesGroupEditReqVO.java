package com.mega.platform.cloud.admin.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigInteger;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 服务组编辑请求参数
 */
@Data
@Accessors(chain = true)
@Schema(name = "服务组编辑请求参数", description = "服务组编辑请求参数")
public class AdminServicesGroupEditReqVO {

    @Schema(hidden = true)
    private Long adminUserId;

    /**
     * 服务组ID
     */
    @NotNull(message = "服务组ID不能为空")
    @Schema(description = "服务组ID", required = true, example = "1")
    private Long servicesGroupId;

    /**
     * 模板参数值（JSON字符串）
     */
    @Schema(description = "模板参数值", required = false,
            example = "{\"appName\":\"test\",\"cloudRestartFileName\":\"测试\",\"gitBranch\":\"master\",\"gitUrl\":\"test\",\"groupId\":\"testGroupId\",\"moduleName\":\"testModuleName\"}")
    private String jenkinsParams;

    /**
     * 模板id
     */
    @Schema(description = "模板id", required = false, example = "1")
    private Long jenkinsTemplateId;

    /**
     * jenkins实例id
     */
    @Schema(description = "jenkins实例id", required = false, example = "1")
    private Long jenkinsServiceId;

    /**
     * 服务组名
     */
    @Schema(description = "服务组名", required = false, example = "user-service-group")
    private String servicesGroupName;

    /**
     * 启动方式
     */
    @Schema(description = "启动方式，1-普通重启 2-滚服重启 3-导流重启 4-脚本运行", required = false, example = "1")
    private Integer serviceUpdateId;

    /**
     * 环境
     */
    @Schema(description = "环境，dev | test | beta | prod", required = false, example = "dev")
    private String serviceEnv;

    /**
     * 保活数量
     */
    @Schema(description = "保活数量，如果是滚服/导流重启配置需要保活的数量", required = false, example = "2")
    private Integer serviceAliveNum = 0;

    /**
     * 保活检测方式
     */
    @Schema(description = "保活检测方式，保活状态检查方式", required = false, example = "1")
    private Integer checkAliveType = 1;

    /**
     * 微服务日志格式id
     */
    @Schema(description = "微服务日志格式id(ext)", required = false)
    private Long servicesLogFormatId;

    /**
     * 1自研 3三方
     */
    @Schema(description = "1自研 3三方（ext）", required = false, example = "1")
    private Integer isSelf;

    /**
     * 备注
     */
    @Schema(description = "备注(ext)", required = false, example = "用户服务组")
    private String remark;

    /**
     * 组标签
     */
    @Schema(description = "组标签(ext)", required = false, example = "[1,2,3]")
    private List<BigInteger> tags;

    /**
     * 是否使用jenkins
     */
    @Schema(description = "是否使用jenkins，0-不用 1-用", required = false, example = "1")
    private Integer useJenkins = 1;

}
