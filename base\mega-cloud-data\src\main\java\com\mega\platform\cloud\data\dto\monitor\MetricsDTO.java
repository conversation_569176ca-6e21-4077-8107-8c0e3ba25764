package com.mega.platform.cloud.data.dto.monitor;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(name = "MetricsDTO", description = "监控指标数据传输对象")
public class MetricsDTO {

    @Schema(description = "指标ID", example = "1001", required = true)
    @NotNull
    private Long metricsId;

    @Schema(description = "指标值", example = "12.34", required = true)
    @NotNull
    private BigDecimal value;

    @Schema(description = "指标key", example = "cpu.usage", required = true)
    @NotNull
    private String key;
}
