package com.mega.platform.cloud.admin.vo;

import com.mega.platform.cloud.data.entity.EcsServer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@Schema(description = "Ecs Server列表响应数据")
public class AdminBaseEcsServerListRespVO {

    @Schema(description = "Ecs Server列表")
    private List<EcsServer> ecsServerList;
}
