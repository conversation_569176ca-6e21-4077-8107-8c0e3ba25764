package com.mega.platform.cloud.data.annotation;

/**
 * @Description:
 * @Author: sys
 * @Date: 2025/8/15 15:35
 * @Version: 1.0
 */
public @interface DescriptionTag {
    /**
     * 描述信息（必填）
     *
     * <p>支持多行描述，使用\n分隔</p>
     */
    String name();/**
     * 标签分类（可选）
     *
     * <p>用于对标签进行分组管理</p>
     */
    String category() default "default";

    /**
     * 创建时间（可选）
     *
     * <p>格式：yyyy-MM-dd HH:mm:ss</p>
     */
    String createdAt() default "";

    /**
     * 最后更新（可选）
     */
    String updatedAt() default "";

    /**
     * 最后更新（可选）
     */
    String adminUser() default "admin";
}
