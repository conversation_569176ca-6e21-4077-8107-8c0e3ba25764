package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 权限管理-角色项目绑定列表响应参数
 */
@Data
@Accessors(chain = true)
@Schema(name = "AdminAccessRoleProjectBindingListRespVO", description = "权限管理-角色项目绑定列表响应参数")
public class AdminAccessRoleProjectBindingListRespVO {

    @Schema(description = "角色ID", example = "1")
    private Long adminRoleId;

    // @Schema(description = "角色名称", example = "管理员")
    // private String roleName;

    @Schema(description = "项目ID", example = "1")
    private Long projectId;

    @Schema(description = "项目名称", example = "示例项目")
    private String projectName;

    // @Schema(description = "创建时间", example = "2023-01-01 12:00:00")
    // private LocalDateTime createTime;

    @Schema(description = "删除标识(0=正常,1=删除)", example = "0")
    private Integer delsign;
}
