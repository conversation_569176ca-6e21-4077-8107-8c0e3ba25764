package com.mega.platform.cloud.admin.dao;

import com.mega.platform.cloud.admin.vo.AdminServicesGroupQueryReqVO;
import com.mega.platform.cloud.admin.vo.AdminServicesGroupRespVO;
import com.mega.platform.cloud.admin.vo.AdminServicesGroupRunningJobRespVO;
import com.mega.platform.cloud.data.dto.jenkins.JenkinsTemplateParamDTO;
import com.mega.platform.cloud.data.entity.ServicesGroup;
import com.mega.platform.cloud.data.entity.ServicesGroupTagRelation;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.common.Mapper;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

/**
 * 服务组管理数据访问层
 */
@Repository
public interface AdminServiceGroupDao extends Mapper<ServicesGroup> {
    
    /**
     * 根据项目ID和服务组名查询服务组（排除已删除）
     * @param projectId 项目ID
     * @param name 服务组名称
     * @param excludeId 排除的服务组ID
     * @return 服务组信息
     */
    ServicesGroup selectServicesGroupByProjectIdAndName(@Param("projectId") Long projectId, 
                                                       @Param("name") String name,
                                                       @Param("excludeId") Long excludeId);
    
    /**
     * 根据ID查询服务组（排除已删除）
     * @param id 服务组ID
     * @return 服务组信息
     */
    ServicesGroup selectServicesGroupById(@Param("id") Long id);
    
    /**
     * 根据项目ID查询服务组列表（排除已删除）
     * @param projectId 项目ID
     * @return 服务组列表
     */
    List<ServicesGroup> selectServicesGroupListByProjectId(@Param("projectId") Long projectId);
    
    /**
     * 检查服务组下是否有运行中的服务
     * @param servicesGroupId 服务组ID
     * @return 运行中的服务数量
     */
    Integer countRunningServicesByServicesGroupId(@Param("servicesGroupId") Long servicesGroupId);
    
    /**
     * 检查服务组下是否有服务（不论状态）
     * @param servicesGroupId 服务组ID
     * @return 服务数量
     */
    Integer countServicesByServicesGroupId(@Param("servicesGroupId") Long servicesGroupId);
    
    /**
     * 更新服务组状态
     * @param id 服务组ID
     * @param status 状态
     * @return 影响行数
     */
    int updateServicesGroupStatus(@Param("id") Long id, @Param("status") Integer status);
    
    /**
     * 逻辑删除服务组
     * @param id 服务组ID
     * @return 影响行数
     */
    int deleteServicesGroupById(@Param("id") Long id);
    
    /**
     * 更新服务组信息（编辑时使用）
     * @param servicesGroup 服务组信息
     * @return 影响行数
     */
    int updateServicesGroupSelective( @Param("servicesGroup") ServicesGroup servicesGroup);

    /**
     * 插入服务组标签关系
     */
    void insertServicesGroupTagRelation( @Param("tagRelations")  List<ServicesGroupTagRelation> tagRelations);


    /**
     * 查询服务组标签关系
     */
    List<ServicesGroupTagRelation> selectServicesGroupTagRelation( @Param("servicesGroupId") Long servicesGroupId);

    /**
     *
     * @param servicesGroupId
     * @param jenkinsParams
     */
    void updateJenkinsTemplateParams(@Param("servicesGroupId") Long servicesGroupId, @Param("jenkinsParams") List<JenkinsTemplateParamDTO> jenkinsParams);

    /**
     * 查询服务组列表
     */
    List<AdminServicesGroupRespVO> selectServicesGroups(@Param("projectId") Long projectId, @Param("reqVO") AdminServicesGroupQueryReqVO reqVO);

    /**
     * 查询正在执行的列表
     */
    List<AdminServicesGroupRunningJobRespVO> selectListServicesJobGroups(@Param("projectId") Long projectId);

    List<AdminServicesGroupRunningJobRespVO> selectListServicesJobHistoryGroups(@Param("servicesGroupIds") List<Long> servicesGroupIds);
}
