package com.mega.platform.cloud.payment.controller;

import com.mega.platform.cloud.client.payment.PaymentVerificationClient;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.vo.payment.*;
import com.mega.platform.cloud.payment.service.PaymentAlipayVerificationService;
import com.mega.platform.cloud.payment.service.PaymentAppleVerificationService;
import com.mega.platform.cloud.payment.service.PaymentWeChatVerificationService;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

@Tag(name = "支付相关接口")
@Slf4j
@RestController
@RequiredArgsConstructor
public class PaymentVerificationController implements PaymentVerificationClient {
    private final PaymentAppleVerificationService paymentAppleVerificationService;
    private final PaymentAlipayVerificationService paymentAlipayVerificationService;
    private final PaymentWeChatVerificationService paymentWeChatVerificationService;

    @Override
    @Operation(summary = "验证苹果交易")
    public Result<PaymentAppleVerifyTransactionRespVO> verifyAppleTransaction(PaymentAppleVerifyTransactionReqVO vo) throws IOException {
        return Results.success(paymentAppleVerificationService.verifyAppleTransaction(vo));
    }

    @Override
    @Operation(summary = "苹果支付回调")
    public Result<PaymentAppleCallbackRespVO> appleCallback(PaymentAppleCallbackReqVO vo){
        return Results.success(paymentAppleVerificationService.appleCallback(vo));
    }

    @Override
    @Operation(summary = "创建支付宝交易")
    public Result<PaymentAlipayCreateRespVO> createAlipayTransaction(PaymentAlipayCreateReqVO vo) {
        return Results.success(paymentAlipayVerificationService.createAlipayTransaction(vo));
    }

    @Override
    @Operation(summary = "支付宝支付回调")
    public Result<PaymentAlipayCallbackRespVO> alipayCallback(PaymentAlipayCallbackReqVO vo) {
        return Results.success(paymentAlipayVerificationService.alipayCallback(vo));
    }

    @Override
    @Operation(summary = "创建微信交易")
    public Result<PaymentWeChatCreateRespVO> createWeChatTransaction(PaymentWeChatCreateReqVO vo) {
        return Results.success(paymentWeChatVerificationService.createWeChatTransaction(vo));
    }

    @Override
    @Operation(summary = "微信支付回调")
    public Result<PaymentWeChatCallbackRespVO> wechatCallback(PaymentWeChatCallbackReqVO vo) {
        return Results.success(paymentWeChatVerificationService.wechatCallback(vo));
    }
}
