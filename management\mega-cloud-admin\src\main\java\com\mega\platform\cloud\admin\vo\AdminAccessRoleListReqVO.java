package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 权限管理-角色列表查询请求
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限管理-角色列表查询请求")
public class AdminAccessRoleListReqVO {

    // @Schema(description = "页码")
    // private Integer pageNum = 1;

    // @Schema(description = "每页大小")
    // private Integer pageSize = 10;

    @Schema(description = "角色名称")
    private String name;

    @Schema(description = "删除标识: 0=未删除, 1=已删除")
    private Integer delsign;
}
