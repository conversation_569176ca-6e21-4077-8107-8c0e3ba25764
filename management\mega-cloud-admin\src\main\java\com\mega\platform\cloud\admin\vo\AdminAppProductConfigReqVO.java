package com.mega.platform.cloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(description = "应用商品配置请求参数")
public class AdminAppProductConfigReqVO {

    @Schema(description = "关联的支付平台ID", example = "123")
    private Long thirdPlatformId;

    @Schema(description = "projectAppId", example = "456")
    private Long projectAppId;

    @Schema(description = "外部商品ID（比如苹果平台的sku）", example = "com.apple.sku123")
    private String externalProductId;

    @Schema(description = "商品名称", example = "高级会员")
    private String productName;

    @Schema(description = "是否为订阅产品", example = "true")
    private Boolean isSubscription;

    @Schema(description = "订阅周期（天）", example = "30")
    private Integer subscriptionPeriodDay;

    @Schema(description = "商品价格", example = "99.99")
    private BigDecimal amount;

    @Schema(description = "币种", example = "CNY")
    private String currency;

    @Schema(description = "备注", example = "特别优惠")
    private String remark;
}
