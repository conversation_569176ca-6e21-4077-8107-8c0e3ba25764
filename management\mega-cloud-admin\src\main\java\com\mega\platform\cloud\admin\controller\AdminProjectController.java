package com.mega.platform.cloud.admin.controller;

import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.admin.constant.AdminAuthConstant;
import com.mega.platform.cloud.admin.service.AdminProjectService;
import com.mega.platform.cloud.admin.vo.AdminProjectVO;
import com.mega.platform.cloud.core.PageResult;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 项目管理控制器
 */
@RestController
@Tag(name = "项目管理")
@Slf4j
public class AdminProjectController {

    private final AdminProjectService adminProjectService;

    @Autowired
    public AdminProjectController(AdminProjectService adminProjectService) {
        this.adminProjectService = adminProjectService;
    }

    /****************************** 系统权限  *******************/
    /**
     * 项目列表查询
     */
    @PostMapping("/system/s-project/list")
    @Operation(summary = "系统级项目列表查询")
    public Result<PageResult<AdminProjectVO>> list(@Valid @RequestBody AdminProjectListReqVO reqVO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        PageResult<AdminProjectVO> result = adminProjectService.findProjectList(reqVO);
        return Results.success(result);
    }

    /**
     * 系统项目详情查询
     */
    @PostMapping("/system/s-project/detail")
    @Operation(summary = "系统级项目详情查询")
    public Result<AdminProjectVO> systemDetail(@Valid @RequestBody AdminSystemProjectDetailReqVO reqVO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        AdminProjectVO result = adminProjectService.getProjectById(reqVO.getProjectId());
        return Results.success(result);
    }

    /**
     * 系统项目创建
     */
    @PostMapping("/system/s-project/create")
    @Operation(summary = "系统级项目创建")
    public Result<Long> systemCreate(@Valid @RequestBody AdminProjectCreateReqVO createPO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        Long projectId = adminProjectService.createProject(createPO);
        return Results.success(projectId);
    }

    /**
     * 系统项目编辑
     */
    @PostMapping("/system/s-project/edit")
    @Operation(summary = "系统级项目编辑")
    public Result<?> systemEdit(@Valid @RequestBody AdminSystemProjectEditReqVO updatePO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        adminProjectService.editProject(updatePO.getProjectId(), updatePO);
        return Results.success();
    }

    /**
     * 系统项目删除
     */
    @PostMapping("/system/s-project/delete")
    @Operation(summary = "系统级项目删除")
    public Result<?> systemDelete(@Valid @RequestBody AdminSystemProjectDetailReqVO reqVO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        adminProjectService.deleteProject(reqVO.getProjectId());
        return Results.success();
    }

    /****************************** 项目权限  *******************/
    /**
     * 项目详情查询
     */
    @PostMapping("/{projectId}/project/detail")
    @Operation(summary = "项目详情查询")
    public Result<AdminProjectVO> detail(@PathVariable("projectId") Long projectId) {
        AdminProjectVO result = adminProjectService.getProjectById(projectId);
        return Results.success(result);
    }

    /**
     * 编辑项目
     */
    @PostMapping("/{projectId}/project/edit")
    @Operation(summary = "项目编辑")
    public Result<?> edit(@PathVariable("projectId") Long projectId,
                          @Valid @RequestBody AdminProjectEditReqVO updatePO) {
        adminProjectService.editProject(projectId, updatePO);
        return Results.success();
    }

    /**
     * 删除项目
     */
    @PostMapping("/{projectId}/project/delete")
    @Operation(summary = "项目删除")
    public Result<?> delete(@PathVariable("projectId") Long projectId) {
        adminProjectService.deleteProject(projectId);
        return Results.success();
    }
}
