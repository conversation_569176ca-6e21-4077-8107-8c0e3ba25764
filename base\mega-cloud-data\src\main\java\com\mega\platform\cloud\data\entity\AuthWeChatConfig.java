package com.mega.platform.cloud.data.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "AuthWeChatConfig", description = "微信平台配置")
public class AuthWeChatConfig {

    @Schema(description = "微信 App ID", example = "wx1234567890abcdef")
    @JsonProperty("appId")
    private String appId;

    @Schema(description = "微信 App Secret", example = "abcdef1234567890abcdef")
    @JsonProperty("appSecret")
    private String appSecret;

    @Schema(description = "微信小程序 App ID", example = "wx0987654321fedcba")
    @JsonProperty("miniAppId")
    private String miniAppId;

    @Schema(description = "微信小程序 App Secret", example = "fedcba0987654321fedcba")
    @JsonProperty("miniAppSecret")
    private String miniAppSecret;
}
